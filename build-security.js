#!/usr/bin/env node

/**
 * Enhanced Security Build Script for ForgeX
 * This script builds and heavily obfuscates security-related JavaScript files
 */

const fs = require('fs');
const path = require('path');
const JavaScriptObfuscator = require('javascript-obfuscator');
const crypto = require('crypto');

// Configuration
const CONFIG = {
    inputDir: './static/js/security',
    outputDir: './static/dist/security',
    manifestFile: './static/dist/security-manifest.json',
    obfuscationLevel: 'maximum' // 'basic', 'medium', 'maximum'
};

// Obfuscation settings based on level
const OBFUSCATION_SETTINGS = {
    basic: {
        compact: true,
        controlFlowFlattening: false,
        deadCodeInjection: false,
        debugProtection: false,
        disableConsoleOutput: true,
        identifierNamesGenerator: 'mangled',
        stringArray: true,
        stringArrayThreshold: 0.5
    },
    medium: {
        compact: true,
        controlFlowFlattening: true,
        controlFlowFlatteningThreshold: 0.5,
        deadCodeInjection: true,
        deadCodeInjectionThreshold: 0.3,
        debugProtection: true,
        debugProtectionInterval: 1000,
        disableConsoleOutput: true,
        identifierNamesGenerator: 'hexadecimal',
        stringArray: true,
        stringArrayCallsTransform: true,
        stringArrayEncoding: ['base64'],
        stringArrayThreshold: 0.7,
        selfDefending: true
    },
    maximum: {
        compact: true,
        controlFlowFlattening: true,
        controlFlowFlatteningThreshold: 0.9,
        deadCodeInjection: true,
        deadCodeInjectionThreshold: 0.6,
        debugProtection: true,
        debugProtectionInterval: 500,
        disableConsoleOutput: true,
        
        // Identifier obfuscation
        identifierNamesGenerator: 'hexadecimal',
        identifiersPrefix: 'forgex_sec_',
        renameGlobals: true,
        renameProperties: false,
        
        // String obfuscation (maximum)
        stringArray: true,
        stringArrayCallsTransform: true,
        stringArrayCallsTransformThreshold: 0.9,
        stringArrayEncoding: ['base64', 'rc4'],
        stringArrayIndexShift: true,
        stringArrayRotate: true,
        stringArrayShuffle: true,
        stringArrayWrappersCount: 3,
        stringArrayWrappersChainedCalls: true,
        stringArrayWrappersParametersMaxCount: 5,
        stringArrayWrappersType: 'function',
        stringArrayThreshold: 0.9,
        
        // Advanced obfuscation
        selfDefending: true,
        simplify: true,
        splitStrings: true,
        splitStringsChunkLength: 3,
        numbersToExpressions: true,
        transformObjectKeys: true,
        unicodeEscapeSequence: true,
        
        // Anti-debugging
        target: 'browser',
        log: false
    }
};

// Files to process
const SECURITY_FILES = [
    'advanced-security.js',
    'source-protection.js',
    'console-blocker.js',
    'devtools-protection.js',
    'security-bundle.js'
];

class SecurityBuilder {
    constructor() {
        this.manifest = {
            buildTime: new Date().toISOString(),
            version: '3.0.0',
            obfuscationLevel: CONFIG.obfuscationLevel,
            files: {}
        };
    }
    
    // Generate content hash for cache busting
    generateHash(content) {
        return crypto.createHash('sha256').update(content).digest('hex').substring(0, 12);
    }
    
    // Create output directory
    ensureOutputDir() {
        if (!fs.existsSync(CONFIG.outputDir)) {
            fs.mkdirSync(CONFIG.outputDir, { recursive: true });
            console.log(`✅ Created output directory: ${CONFIG.outputDir}`);
        }
    }
    
    // Read and process a security file
    processFile(filename) {
        const inputPath = path.join(CONFIG.inputDir, filename);
        
        if (!fs.existsSync(inputPath)) {
            console.warn(`⚠️  File not found: ${inputPath}`);
            return null;
        }
        
        console.log(`🔄 Processing: ${filename}`);
        
        try {
            // Read source file
            const sourceCode = fs.readFileSync(inputPath, 'utf8');
            
            // Add anti-tampering header
            const protectedCode = this.addAntiTamperingHeader(sourceCode, filename);
            
            // Obfuscate the code
            const obfuscationOptions = OBFUSCATION_SETTINGS[CONFIG.obfuscationLevel];
            const obfuscationResult = JavaScriptObfuscator.obfuscate(protectedCode, obfuscationOptions);
            const obfuscatedCode = obfuscationResult.getObfuscatedCode();
            
            // Generate hash for filename
            const hash = this.generateHash(obfuscatedCode);
            const baseName = path.parse(filename).name;
            const outputFilename = `${baseName}.${hash}.min.js`;
            const outputPath = path.join(CONFIG.outputDir, outputFilename);
            
            // Write obfuscated file
            fs.writeFileSync(outputPath, obfuscatedCode);
            
            // Update manifest
            this.manifest.files[baseName] = {
                original: filename,
                obfuscated: outputFilename,
                hash: hash,
                size: obfuscatedCode.length,
                timestamp: new Date().toISOString()
            };
            
            console.log(`✅ Obfuscated: ${filename} -> ${outputFilename} (${obfuscatedCode.length} bytes)`);
            
            return {
                filename: outputFilename,
                content: obfuscatedCode,
                hash: hash
            };
            
        } catch (error) {
            console.error(`❌ Error processing ${filename}:`, error.message);
            return null;
        }
    }
    
    // Add anti-tampering protection to source code
    addAntiTamperingHeader(sourceCode, filename) {
        const timestamp = Date.now();
        const checksum = this.generateHash(sourceCode);
        
        const header = `
/**
 * ForgeX Security Module - ${filename}
 * Build: ${timestamp} | Checksum: ${checksum}
 * WARNING: This file is protected by advanced security measures.
 * Any attempt to modify, reverse engineer, or tamper with this code
 * will be detected and logged for security purposes.
 */

(function() {
    'use strict';
    
    // Anti-tampering check
    const EXPECTED_CHECKSUM = '${checksum}';
    const BUILD_TIMESTAMP = ${timestamp};
    
    // Verify integrity
    if (typeof window !== 'undefined') {
        const currentScript = document.currentScript;
        if (currentScript) {
            const scriptContent = currentScript.innerHTML || '';
            // Additional integrity checks can be added here
        }
    }
    
    // Original code follows
    ${sourceCode}
    
})();
        `;
        
        return header;
    }
    
    // Create a combined security bundle
    createCombinedBundle(processedFiles) {
        console.log('🔄 Creating combined security bundle...');
        
        const validFiles = processedFiles.filter(file => file !== null);
        
        if (validFiles.length === 0) {
            console.warn('⚠️  No valid files to combine');
            return;
        }
        
        // Combine all obfuscated code
        const combinedCode = validFiles.map(file => file.content).join('\n\n');
        
        // Add bundle wrapper
        const bundleCode = `
/**
 * ForgeX Security Bundle - Combined Protection System
 * Build: ${new Date().toISOString()}
 * Files: ${validFiles.length}
 */

(function() {
    'use strict';
    
    // Bundle initialization
    console.log('🛡️ ForgeX Security Bundle Loading...');
    
    ${combinedCode}
    
    // Bundle completion
    console.log('🛡️ ForgeX Security Bundle Active');
    
})();
        `;
        
        // Obfuscate the entire bundle
        const obfuscationOptions = OBFUSCATION_SETTINGS[CONFIG.obfuscationLevel];
        const bundleResult = JavaScriptObfuscator.obfuscate(bundleCode, obfuscationOptions);
        const obfuscatedBundle = bundleResult.getObfuscatedCode();
        
        // Generate bundle filename
        const bundleHash = this.generateHash(obfuscatedBundle);
        const bundleFilename = `security-bundle.${bundleHash}.min.js`;
        const bundlePath = path.join(CONFIG.outputDir, bundleFilename);
        
        // Write bundle
        fs.writeFileSync(bundlePath, obfuscatedBundle);
        
        // Update manifest
        this.manifest.bundle = {
            filename: bundleFilename,
            hash: bundleHash,
            size: obfuscatedBundle.length,
            files: validFiles.length,
            timestamp: new Date().toISOString()
        };
        
        console.log(`✅ Created security bundle: ${bundleFilename} (${obfuscatedBundle.length} bytes)`);
    }
    
    // Save manifest file
    saveManifest() {
        const manifestPath = CONFIG.manifestFile;
        const manifestDir = path.dirname(manifestPath);
        
        if (!fs.existsSync(manifestDir)) {
            fs.mkdirSync(manifestDir, { recursive: true });
        }
        
        fs.writeFileSync(manifestPath, JSON.stringify(this.manifest, null, 2));
        console.log(`✅ Saved manifest: ${manifestPath}`);
    }
    
    // Main build process
    build() {
        console.log('🚀 Starting ForgeX Security Build Process...');
        console.log(`📁 Input: ${CONFIG.inputDir}`);
        console.log(`📁 Output: ${CONFIG.outputDir}`);
        console.log(`🔒 Obfuscation Level: ${CONFIG.obfuscationLevel}`);
        console.log('');
        
        // Ensure output directory exists
        this.ensureOutputDir();
        
        // Process each security file
        const processedFiles = SECURITY_FILES.map(filename => this.processFile(filename));
        
        // Create combined bundle
        this.createCombinedBundle(processedFiles);
        
        // Save manifest
        this.saveManifest();
        
        console.log('');
        console.log('🎉 Security build completed successfully!');
        console.log(`📊 Processed ${processedFiles.filter(f => f !== null).length} files`);
        console.log(`📦 Bundle size: ${this.manifest.bundle ? this.manifest.bundle.size : 0} bytes`);
        console.log('');
        console.log('🔐 All security files have been heavily obfuscated and are ready for production use.');
    }
}

// Run the build process
if (require.main === module) {
    const builder = new SecurityBuilder();
    builder.build();
}

module.exports = SecurityBuilder;
