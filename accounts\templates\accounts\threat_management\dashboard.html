{% extends 'base.html' %}
{% load static %}

{% block title %}Security Dashboard - ForgeX{% endblock %}

{% block content %}
<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<section class="collaborate-page">
  <div class="collaborate-card fade-in">
    <!-- Messages -->
    {% if messages %}
    <div class="messages-container">
      {% for message in messages %}
        <div class="alert alert-{{ message.tags }} slide-down">
          <span class="alert-icon">
            {% if message.tags == 'success' %}🎉{% elif message.tags == 'info' %}ℹ️{% elif message.tags == 'warning' %}⚠️{% else %}❌{% endif %}
          </span>
          {{ message }}
          <button class="alert-close" onclick="this.parentElement.style.display='none'">×</button>
        </div>
      {% endfor %}
    </div>
    {% endif %}

    <!-- Security Dashboard Header -->
    <div class="dashboard-welcome">
      <h1 class="nunito-sans-header">🛡️ Security Dashboard</h1>
      <p class="welcome-subtitle">Monitor and respond to security threats across the ForgeX platform</p>
    </div>

    <!-- Security Stats -->
    <div class="dashboard-stats">
      <div class="stat-card">
        <div class="stat-icon">📊</div>
        <div class="stat-content">
          <h3>{{ stats.total_events_today }}</h3>
          <p>Events Today</p>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">⚠️</div>
        <div class="stat-content">
          <h3>{{ stats.warnings_sent_today }}</h3>
          <p>Warnings Sent</p>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">🚫</div>
        <div class="stat-content">
          <h3>{{ stats.active_blocks }}</h3>
          <p>Active IP Blocks</p>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">📋</div>
        <div class="stat-content">
          <h3>{{ stats.pending_warnings }}</h3>
          <p>Pending Warnings</p>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">🎯</div>
        <div class="stat-content">
          <h3>{{ stats.high_risk_users }}</h3>
          <p>High Risk Users</p>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="dashboard-section">
      <h2>🛡️ Security Management</h2>
      <div class="action-grid">
        <a href="{% url 'accounts:security_events_list' %}" class="action-card">
          <div class="action-icon">📋</div>
          <h3>View All Events</h3>
          <p>Monitor and review all security events across the platform</p>
        </a>
        <a href="{% url 'accounts:user_threat_levels' %}" class="action-card">
          <div class="action-icon">🎯</div>
          <h3>Threat Levels</h3>
          <p>Manage user threat levels and security classifications</p>
        </a>
        <a href="{% url 'accounts:ip_blocks_list' %}" class="action-card">
          <div class="action-icon">🚫</div>
          <h3>IP Blocks</h3>
          <p>View and manage blocked IP addresses and restrictions</p>
        </a>
        <button class="action-card action-button" onclick="showBulkActions()">
          <div class="action-icon">⚙️</div>
          <h3>Bulk Actions</h3>
          <p>Perform bulk security operations and management tasks</p>
        </button>
      </div>
    </div>

    <!-- Recent Security Events Section -->
    <div class="dashboard-section">
      <h2>🚨 Recent Security Events</h2>
      <div class="security-events-container">
        <div class="security-events-grid">
          {% for event in recent_events %}
          <div class="security-event-card">
            <div class="event-header">
              <h4>{{ event.get_event_type_display }}</h4>
              <span class="severity-badge severity-{{ event.severity }}">{{ event.severity|title }}</span>
            </div>
            <div class="event-content">
              <div class="event-user">
                {% if event.user %}
                  <strong>{{ event.user.username }}</strong>
                {% else %}
                  <em>Anonymous User</em>
                {% endif %}
              </div>
              <div class="event-meta">
                <span class="event-time">{{ event.timestamp|date:"M d, Y H:i:s" }}</span>
                <span class="event-ip">IP: {{ event.ip_address }}</span>
              </div>
            </div>
            {% if event.user %}
            <div class="event-actions">
              <button class="btn btn-primary btn-sm" onclick="showWarningModal({{ event.user.id }}, {{ event.id }})">
                <i class="fas fa-exclamation-triangle"></i> Warn
              </button>
              <button class="btn btn-secondary btn-sm" onclick="showBlockModal('{{ event.ip_address }}', {{ event.user.id }}, [{{ event.id }}])">
                <i class="fas fa-ban"></i> Block
              </button>
              <a href="{% url 'accounts:user_security_profile' event.user.id %}" class="btn btn-outline btn-sm">
                <i class="fas fa-user"></i> Profile
              </a>
            </div>
            {% endif %}
          </div>
          {% empty %}
          <div class="no-events-message">
            <div class="no-events-icon">🛡️</div>
            <h3>All Clear!</h3>
            <p>No recent security events to display. The platform is secure.</p>
          </div>
          {% endfor %}
        </div>

        {% if recent_events %}
        <div class="view-all-events">
          <a href="{% url 'accounts:security_events_list' %}" class="btn btn-secondary">View All Events</a>
        </div>
        {% endif %}
      </div>
    </div>

    <!-- High Risk Users Section -->
    {% if high_risk_users %}
    <div class="dashboard-section">
      <h2>🎯 High Risk Users</h2>
      <div class="high-risk-users-grid">
        {% for threat_level in high_risk_users %}
        <div class="user-threat-card">
          <div class="user-header">
            <h4>{{ threat_level.user.username }}</h4>
            <span class="threat-badge threat-level-{{ threat_level.level }}">{{ threat_level.get_level_display }}</span>
          </div>
          <div class="user-stats">
            <div class="stat-item">
              <span class="stat-label">Violations</span>
              <span class="stat-value">{{ threat_level.total_violations }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Warnings</span>
              <span class="stat-value">{{ threat_level.warnings_received }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Last Violation</span>
              <span class="stat-value">{{ threat_level.last_violation|date:"M d, Y" }}</span>
            </div>
          </div>
          <div class="user-actions">
            <button class="btn btn-primary btn-sm" onclick="showWarningModal({{ threat_level.user.id }})">
              <i class="fas fa-exclamation-triangle"></i> Send Warning
            </button>
            <a href="{% url 'accounts:user_security_profile' threat_level.user.id %}" class="btn btn-secondary btn-sm">
              <i class="fas fa-user"></i> View Profile
            </a>
          </div>
        </div>
        {% endfor %}
      </div>
      <div class="view-all-users">
        <a href="{% url 'accounts:user_threat_levels' %}" class="btn btn-secondary">View All Threat Levels</a>
      </div>
    </div>
    {% endif %}

    <!-- Recent Administrative Actions Section -->
    {% if recent_actions %}
    <div class="dashboard-section">
      <h2>📋 Recent Administrative Actions</h2>
      <div class="recent-actions-list">
        {% for action in recent_actions %}
        <div class="action-card-mini">
          <div class="action-header">
            <h4>{{ action.get_action_type_display }}</h4>
            <span class="action-date">{{ action.created_at|date:"M d, Y H:i" }}</span>
          </div>
          <div class="action-details">
            <span class="action-admin">by {{ action.admin.username }}</span>
            {% if action.target_user %}
              <span class="action-target">→ {{ action.target_user.username }}</span>
            {% endif %}
          </div>
          <p class="action-description">{{ action.description|truncatewords:15 }}</p>
        </div>
        {% endfor %}
      </div>
    </div>
    {% endif %}
  </div>
</section>

<!-- Warning Modal -->
<div class="modal fade" id="warningModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Send Security Warning</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="warningForm">
                    <input type="hidden" id="warning-user-id">
                    <input type="hidden" id="warning-security-log-id">

                    <div class="mb-3">
                        <label for="warning-template" class="form-label">Warning Template</label>
                        <select class="form-select" id="warning-template" onchange="loadTemplate()">
                            <option value="">Custom Message</option>
                            {% for key, template in warning_templates.items %}
                            <option value="{{ key }}">{{ template.title }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="warning-title" class="form-label">Title</label>
                        <input type="text" class="form-control" id="warning-title" required>
                    </div>

                    <div class="mb-3">
                        <label for="warning-message" class="form-label">Message</label>
                        <textarea class="form-control" id="warning-message" rows="4" required></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="warning-severity" class="form-label">Severity</label>
                        <select class="form-select" id="warning-severity" required>
                            <option value="warning">Warning</option>
                            <option value="final_warning">Final Warning</option>
                            <option value="critical">Critical</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" onclick="sendWarning()">Send Warning</button>
            </div>
        </div>
    </div>
</div>

<!-- Block IP Modal -->
<div class="modal fade" id="blockModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Block IP Address</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="blockForm">
                    <input type="hidden" id="block-user-id">
                    <input type="hidden" id="block-security-log-ids">

                    <div class="mb-3">
                        <label for="block-ip" class="form-label">IP Address</label>
                        <input type="text" class="form-control" id="block-ip" required readonly>
                    </div>

                    <div class="mb-3">
                        <label for="block-type" class="form-label">Block Type</label>
                        <select class="form-select" id="block-type" onchange="toggleDuration()">
                            <option value="temporary">Temporary</option>
                            <option value="permanent">Permanent</option>
                        </select>
                    </div>

                    <div class="mb-3" id="duration-field">
                        <label for="block-duration" class="form-label">Duration (hours)</label>
                        <input type="number" class="form-control" id="block-duration" value="24" min="1">
                    </div>

                    <div class="mb-3">
                        <label for="block-reason" class="form-label">Reason</label>
                        <select class="form-select" id="block-reason">
                            <option value="security_violation">Security Violation</option>
                            <option value="repeated_warnings">Repeated Warnings Ignored</option>
                            <option value="malicious_activity">Malicious Activity</option>
                            <option value="spam">Spam/Abuse</option>
                            <option value="manual_block">Manual Block</option>
                            <option value="other">Other</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="block-custom-reason" class="form-label">Additional Details</label>
                        <textarea class="form-control" id="block-custom-reason" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="blockIP()">Block IP</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* Dashboard Welcome Section */
    .dashboard-welcome {
      text-align: center;
      margin-bottom: 50px;
      padding: 30px;
      background: linear-gradient(135deg, rgba(192, 255, 107, 0.1) 0%, rgba(160, 224, 102, 0.05) 100%);
      border-radius: 20px;
      border: 1px solid rgba(192, 255, 107, 0.2);
      backdrop-filter: blur(10px);
    }

    .dashboard-welcome h1 {
      color: #C0ff6b;
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 15px;
      text-shadow: 0 0 20px rgba(192, 255, 107, 0.3);
    }

    .welcome-subtitle {
      color: rgba(255, 255, 255, 0.8);
      font-size: 1.1rem;
      margin: 0;
    }

    /* Enhanced Dashboard Stats - Horizontal Layout */
    .dashboard-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
      gap: 25px;
      margin-bottom: 50px;
      padding: 0 10px;
    }

    .stat-card {
      background: linear-gradient(135deg, rgba(40, 40, 40, 0.8) 0%, rgba(60, 60, 60, 0.6) 100%);
      border-radius: 18px;
      padding: 30px 25px;
      text-align: center;
      border: 2px solid rgba(192, 255, 107, 0.2);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
      backdrop-filter: blur(15px);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    }

    .stat-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(192, 255, 107, 0.1), transparent);
      transition: left 0.6s ease;
    }

    .stat-card:hover::before {
      left: 100%;
    }

    .stat-card:hover {
      transform: translateY(-8px) scale(1.02);
      border-color: rgba(192, 255, 107, 0.6);
      box-shadow: 0 20px 40px rgba(192, 255, 107, 0.2), 0 0 30px rgba(192, 255, 107, 0.1);
      background: linear-gradient(135deg, rgba(40, 40, 40, 0.9) 0%, rgba(60, 60, 60, 0.7) 100%);
    }

    .stat-icon {
      font-size: 3rem;
      margin-bottom: 20px;
      display: block;
      filter: drop-shadow(0 0 10px rgba(192, 255, 107, 0.3));
      transition: all 0.3s ease;
    }

    .stat-card:hover .stat-icon {
      transform: scale(1.1);
      filter: drop-shadow(0 0 15px rgba(192, 255, 107, 0.5));
    }

    .stat-content {
      position: relative;
      z-index: 2;
    }

    .stat-content h3 {
      font-size: 2.5rem;
      color: #C0ff6b;
      margin-bottom: 8px;
      font-weight: 700;
      text-shadow: 0 0 10px rgba(192, 255, 107, 0.4);
      transition: all 0.3s ease;
    }

    .stat-card:hover .stat-content h3 {
      color: #ffffff;
      text-shadow: 0 0 15px rgba(192, 255, 107, 0.6);
    }

    .stat-content p {
      color: rgba(255, 255, 255, 0.8);
      margin: 0;
      font-size: 1rem;
      font-weight: 500;
      letter-spacing: 0.5px;
      transition: all 0.3s ease;
    }

    .stat-card:hover .stat-content p {
      color: rgba(255, 255, 255, 0.95);
    }

    /* Dashboard Section Styling - Enhanced */
    .dashboard-section {
      margin-bottom: 50px;
      padding: 35px;
      background: linear-gradient(135deg, rgba(40, 40, 40, 0.6) 0%, rgba(30, 30, 30, 0.4) 100%);
      border-radius: 20px;
      border: 1px solid rgba(192, 255, 107, 0.2);
      backdrop-filter: blur(10px);
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
      transition: all 0.3s ease;
    }

    .dashboard-section:hover {
      border-color: rgba(192, 255, 107, 0.3);
      box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
    }

    .dashboard-section h2 {
      color: #C0ff6b;
      margin-bottom: 35px;
      font-size: 2rem;
      font-weight: 600;
      text-align: center;
      text-shadow: 0 0 15px rgba(192, 255, 107, 0.3);
      position: relative;
      padding-bottom: 15px;
    }

    .dashboard-section h2::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 80px;
      height: 3px;
      background: linear-gradient(90deg, transparent, #C0ff6b, transparent);
      border-radius: 2px;
    }

    /* Enhanced Action Grid */
    .action-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 25px;
      margin-bottom: 40px;
    }

    .action-card {
      background: linear-gradient(135deg, rgba(40, 40, 40, 0.8) 0%, rgba(60, 60, 60, 0.6) 100%);
      border-radius: 16px;
      padding: 30px 25px;
      text-align: center;
      border: 2px solid rgba(192, 255, 107, 0.2);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      text-decoration: none;
      color: inherit;
      display: block;
      position: relative;
      overflow: hidden;
      backdrop-filter: blur(15px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }

    .action-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(192, 255, 107, 0.1), transparent);
      transition: left 0.6s ease;
    }

    .action-card:hover::before {
      left: 100%;
    }

    .action-card:hover {
      transform: translateY(-8px) scale(1.02);
      box-shadow: 0 20px 40px rgba(192, 255, 107, 0.15), 0 0 30px rgba(192, 255, 107, 0.1);
      border-color: rgba(192, 255, 107, 0.5);
      text-decoration: none;
      color: inherit;
      background: linear-gradient(135deg, rgba(40, 40, 40, 0.9) 0%, rgba(60, 60, 60, 0.7) 100%);
    }

    .action-icon {
      font-size: 3rem;
      margin-bottom: 20px;
      display: block;
      filter: drop-shadow(0 0 10px rgba(192, 255, 107, 0.3));
      transition: all 0.3s ease;
      position: relative;
      z-index: 2;
    }

    .action-card:hover .action-icon {
      transform: scale(1.1);
      filter: drop-shadow(0 0 15px rgba(192, 255, 107, 0.5));
    }

    .action-card h3 {
      font-size: 1.3rem;
      color: #C0ff6b;
      margin-bottom: 12px;
      font-weight: 600;
      text-shadow: 0 0 10px rgba(192, 255, 107, 0.3);
      transition: all 0.3s ease;
      position: relative;
      z-index: 2;
    }

    .action-card:hover h3 {
      color: #ffffff;
      text-shadow: 0 0 15px rgba(192, 255, 107, 0.5);
    }

    .action-card p {
      color: rgba(255, 255, 255, 0.7);
      margin: 0;
      font-size: 0.95rem;
      line-height: 1.5;
      transition: all 0.3s ease;
      position: relative;
      z-index: 2;
    }

    .action-card:hover p {
      color: rgba(255, 255, 255, 0.9);
    }

    /* Enhanced Security Events Grid */
    .security-events-container {
      margin-top: 20px;
    }

    .security-events-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
      gap: 25px;
      margin-bottom: 40px;
    }

    .security-event-card {
      background: linear-gradient(135deg, rgba(40, 40, 40, 0.8) 0%, rgba(60, 60, 60, 0.6) 100%);
      border-radius: 16px;
      padding: 30px;
      border: 2px solid rgba(192, 255, 107, 0.2);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
      backdrop-filter: blur(15px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }

    .security-event-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(192, 255, 107, 0.08), transparent);
      transition: left 0.6s ease;
    }

    .security-event-card:hover::before {
      left: 100%;
    }

    .security-event-card:hover {
      transform: translateY(-8px) scale(1.02);
      border-color: rgba(192, 255, 107, 0.5);
      box-shadow: 0 20px 40px rgba(192, 255, 107, 0.15), 0 0 30px rgba(192, 255, 107, 0.1);
      background: linear-gradient(135deg, rgba(40, 40, 40, 0.9) 0%, rgba(60, 60, 60, 0.7) 100%);
    }

    .event-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      position: relative;
      z-index: 2;
    }

    .event-header h4 {
      color: #C0ff6b;
      font-size: 1.2rem;
      margin: 0;
      font-weight: 600;
      text-shadow: 0 0 10px rgba(192, 255, 107, 0.3);
      transition: all 0.3s ease;
    }

    .security-event-card:hover .event-header h4 {
      color: #ffffff;
      text-shadow: 0 0 15px rgba(192, 255, 107, 0.5);
    }

    .severity-badge {
      padding: 8px 16px;
      border-radius: 25px;
      font-size: 0.8rem;
      font-weight: 700;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
      transition: all 0.3s ease;
    }

    .severity-warning {
      background: linear-gradient(135deg, rgba(255, 193, 7, 0.3), rgba(255, 193, 7, 0.2));
      color: #ffc107;
      border: 2px solid rgba(255, 193, 7, 0.4);
    }

    .severity-final_warning {
      background: linear-gradient(135deg, rgba(255, 107, 107, 0.3), rgba(255, 107, 107, 0.2));
      color: #ff6b6b;
      border: 2px solid rgba(255, 107, 107, 0.4);
    }

    .severity-critical {
      background: linear-gradient(135deg, rgba(220, 53, 69, 0.4), rgba(220, 53, 69, 0.3));
      color: #dc3545;
      border: 2px solid rgba(220, 53, 69, 0.5);
      animation: pulse-critical 2s infinite;
    }

    @keyframes pulse-critical {
      0% {
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.6), 0 4px 15px rgba(0, 0, 0, 0.2);
        transform: scale(1);
      }
      70% {
        box-shadow: 0 0 0 15px rgba(220, 53, 69, 0), 0 4px 15px rgba(0, 0, 0, 0.2);
        transform: scale(1.05);
      }
      100% {
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0), 0 4px 15px rgba(0, 0, 0, 0.2);
        transform: scale(1);
      }
    }

    .event-content {
      margin-bottom: 20px;
      position: relative;
      z-index: 2;
    }

    .event-user {
      color: #ffffff;
      font-weight: 600;
      margin-bottom: 12px;
      font-size: 1.05rem;
      transition: all 0.3s ease;
    }

    .security-event-card:hover .event-user {
      color: #C0ff6b;
    }

    .event-meta {
      display: flex;
      flex-direction: column;
      gap: 8px;
      font-size: 0.9rem;
      color: rgba(255, 255, 255, 0.7);
      transition: all 0.3s ease;
    }

    .security-event-card:hover .event-meta {
      color: rgba(255, 255, 255, 0.9);
    }

    .event-actions {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
      position: relative;
      z-index: 2;
    }

    /* Enhanced High Risk Users Grid */
    .high-risk-users-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 25px;
      margin-bottom: 40px;
    }

    .user-threat-card {
      background: linear-gradient(135deg, rgba(40, 40, 40, 0.8) 0%, rgba(60, 60, 60, 0.6) 100%);
      border-radius: 16px;
      padding: 30px;
      border: 2px solid rgba(192, 255, 107, 0.2);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
      backdrop-filter: blur(15px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }

    .user-threat-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(192, 255, 107, 0.08), transparent);
      transition: left 0.6s ease;
    }

    .user-threat-card:hover::before {
      left: 100%;
    }

    .user-threat-card:hover {
      transform: translateY(-8px) scale(1.02);
      border-color: rgba(192, 255, 107, 0.5);
      box-shadow: 0 20px 40px rgba(192, 255, 107, 0.15), 0 0 30px rgba(192, 255, 107, 0.1);
      background: linear-gradient(135deg, rgba(40, 40, 40, 0.9) 0%, rgba(60, 60, 60, 0.7) 100%);
    }

    .user-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      position: relative;
      z-index: 2;
    }

    .user-header h4 {
      color: #C0ff6b;
      font-size: 1.2rem;
      margin: 0;
      font-weight: 600;
      text-shadow: 0 0 10px rgba(192, 255, 107, 0.3);
      transition: all 0.3s ease;
    }

    .user-threat-card:hover .user-header h4 {
      color: #ffffff;
      text-shadow: 0 0 15px rgba(192, 255, 107, 0.5);
    }

    .threat-badge {
      padding: 8px 16px;
      border-radius: 25px;
      font-size: 0.8rem;
      font-weight: 700;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
      transition: all 0.3s ease;
    }

    .threat-level-green {
      background: linear-gradient(135deg, rgba(40, 167, 69, 0.3), rgba(40, 167, 69, 0.2));
      color: #28a745;
      border: 2px solid rgba(40, 167, 69, 0.4);
    }

    .threat-level-yellow {
      background: linear-gradient(135deg, rgba(255, 193, 7, 0.3), rgba(255, 193, 7, 0.2));
      color: #ffc107;
      border: 2px solid rgba(255, 193, 7, 0.4);
    }

    .threat-level-red {
      background: linear-gradient(135deg, rgba(220, 53, 69, 0.3), rgba(220, 53, 69, 0.2));
      color: #dc3545;
      border: 2px solid rgba(220, 53, 69, 0.4);
    }

    .threat-level-critical {
      background: linear-gradient(135deg, rgba(220, 53, 69, 0.4), rgba(220, 53, 69, 0.3));
      color: #dc3545;
      border: 2px solid rgba(220, 53, 69, 0.5);
      animation: pulse-critical 2s infinite;
    }

    .user-stats {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20px;
      margin-bottom: 25px;
      position: relative;
      z-index: 2;
    }

    .stat-item {
      text-align: center;
      padding: 15px 10px;
      background: rgba(192, 255, 107, 0.05);
      border-radius: 12px;
      border: 1px solid rgba(192, 255, 107, 0.1);
      transition: all 0.3s ease;
    }

    .stat-item:hover {
      background: rgba(192, 255, 107, 0.1);
      border-color: rgba(192, 255, 107, 0.2);
      transform: translateY(-2px);
    }

    .stat-label {
      display: block;
      font-size: 0.85rem;
      color: rgba(255, 255, 255, 0.7);
      margin-bottom: 8px;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .stat-item:hover .stat-label {
      color: rgba(255, 255, 255, 0.9);
    }

    .stat-value {
      display: block;
      font-size: 1.4rem;
      font-weight: 700;
      color: #C0ff6b;
      text-shadow: 0 0 10px rgba(192, 255, 107, 0.3);
      transition: all 0.3s ease;
    }

    .stat-item:hover .stat-value {
      color: #ffffff;
      text-shadow: 0 0 15px rgba(192, 255, 107, 0.5);
    }

    .user-actions {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
      position: relative;
      z-index: 2;
    }

    /* Enhanced Recent Actions List */
    .recent-actions-list {
      display: flex;
      flex-direction: column;
      gap: 20px;
      margin-bottom: 40px;
    }

    .action-card-mini {
      background: linear-gradient(135deg, rgba(40, 40, 40, 0.8) 0%, rgba(60, 60, 60, 0.6) 100%);
      border-radius: 16px;
      padding: 25px;
      border: 2px solid rgba(192, 255, 107, 0.2);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
      backdrop-filter: blur(15px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }

    .action-card-mini::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(192, 255, 107, 0.08), transparent);
      transition: left 0.6s ease;
    }

    .action-card-mini:hover::before {
      left: 100%;
    }

    .action-card-mini:hover {
      border-color: rgba(192, 255, 107, 0.5);
      box-shadow: 0 15px 35px rgba(192, 255, 107, 0.15), 0 0 25px rgba(192, 255, 107, 0.1);
      transform: translateY(-5px);
      background: linear-gradient(135deg, rgba(40, 40, 40, 0.9) 0%, rgba(60, 60, 60, 0.7) 100%);
    }

    .action-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      position: relative;
      z-index: 2;
    }

    .action-header h4 {
      color: #C0ff6b;
      font-size: 1.1rem;
      margin: 0;
      font-weight: 600;
      text-shadow: 0 0 10px rgba(192, 255, 107, 0.3);
      transition: all 0.3s ease;
    }

    .action-card-mini:hover .action-header h4 {
      color: #ffffff;
      text-shadow: 0 0 15px rgba(192, 255, 107, 0.5);
    }

    .action-date {
      font-size: 0.9rem;
      color: rgba(255, 255, 255, 0.7);
      transition: all 0.3s ease;
    }

    .action-card-mini:hover .action-date {
      color: rgba(255, 255, 255, 0.9);
    }

    .action-details {
      display: flex;
      gap: 20px;
      margin-bottom: 12px;
      font-size: 0.95rem;
      position: relative;
      z-index: 2;
    }

    .action-admin {
      color: rgba(255, 255, 255, 0.8);
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .action-card-mini:hover .action-admin {
      color: rgba(255, 255, 255, 0.95);
    }

    .action-target {
      color: #C0ff6b;
      font-weight: 600;
      text-shadow: 0 0 8px rgba(192, 255, 107, 0.3);
      transition: all 0.3s ease;
    }

    .action-card-mini:hover .action-target {
      color: #ffffff;
      text-shadow: 0 0 12px rgba(192, 255, 107, 0.5);
    }

    .action-description {
      color: rgba(255, 255, 255, 0.7);
      font-size: 0.9rem;
      margin: 0;
      line-height: 1.5;
      transition: all 0.3s ease;
      position: relative;
      z-index: 2;
    }

    .action-card-mini:hover .action-description {
      color: rgba(255, 255, 255, 0.9);
    }

    /* Enhanced No Content Messages */
    .no-events-message {
      text-align: center;
      padding: 4rem 2rem;
      grid-column: 1 / -1;
      background: linear-gradient(135deg, rgba(192, 255, 107, 0.05) 0%, rgba(160, 224, 102, 0.02) 100%);
      border-radius: 16px;
      border: 2px dashed rgba(192, 255, 107, 0.3);
      transition: all 0.3s ease;
    }

    .no-events-message:hover {
      border-color: rgba(192, 255, 107, 0.5);
      background: linear-gradient(135deg, rgba(192, 255, 107, 0.08) 0%, rgba(160, 224, 102, 0.04) 100%);
    }

    .no-events-icon {
      font-size: 4rem;
      margin-bottom: 20px;
      filter: drop-shadow(0 0 15px rgba(192, 255, 107, 0.3));
      animation: float 3s ease-in-out infinite;
    }

    @keyframes float {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
    }

    .no-events-message h3 {
      color: #C0ff6b;
      margin-bottom: 15px;
      font-size: 1.5rem;
      font-weight: 600;
      text-shadow: 0 0 15px rgba(192, 255, 107, 0.3);
    }

    .no-events-message p {
      color: rgba(255, 255, 255, 0.8);
      font-size: 1.05rem;
      line-height: 1.6;
    }

    /* Enhanced Action Button Styling */
    .action-button {
      background: none;
      border: none;
      cursor: pointer;
      text-align: center;
      width: 100%;
      padding: 30px 25px;
      position: relative;
      overflow: hidden;
    }

    .action-button:hover {
      transform: translateY(-8px) scale(1.02);
      box-shadow: 0 20px 40px rgba(192, 255, 107, 0.15), 0 0 30px rgba(192, 255, 107, 0.1);
      border-color: rgba(192, 255, 107, 0.5);
    }

    /* Enhanced Button Styling */
    .btn {
      padding: 12px 25px;
      border-radius: 12px;
      text-decoration: none;
      font-weight: 600;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      display: inline-block;
      position: relative;
      overflow: hidden;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      transition: left 0.6s ease;
    }

    .btn:hover::before {
      left: 100%;
    }

    .btn-secondary {
      background: linear-gradient(135deg, rgba(192, 255, 107, 0.15) 0%, rgba(192, 255, 107, 0.1) 100%);
      color: #C0ff6b;
      border: 2px solid rgba(192, 255, 107, 0.3);
      text-shadow: 0 0 10px rgba(192, 255, 107, 0.3);
    }

    .btn-secondary:hover {
      background: linear-gradient(135deg, rgba(192, 255, 107, 0.25) 0%, rgba(192, 255, 107, 0.2) 100%);
      transform: translateY(-3px) scale(1.05);
      border-color: rgba(192, 255, 107, 0.5);
      color: #ffffff;
      text-shadow: 0 0 15px rgba(192, 255, 107, 0.5);
      box-shadow: 0 10px 25px rgba(192, 255, 107, 0.2);
    }

    /* Enhanced View All Buttons */
    .view-all-events,
    .view-all-users {
      text-align: center;
      margin-top: 30px;
      padding: 20px;
    }

    /* Enhanced Responsive Design */
    @media (max-width: 1200px) {
      .dashboard-stats {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
      }

      .action-grid {
        grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
      }

      .security-events-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      }

      .high-risk-users-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
      }
    }

    @media (max-width: 768px) {
      .dashboard-welcome {
        padding: 25px 20px;
        margin-bottom: 40px;
      }

      .dashboard-welcome h1 {
        font-size: 2rem;
      }

      .welcome-subtitle {
        font-size: 1rem;
      }

      .dashboard-stats {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 0 5px;
      }

      .stat-card {
        padding: 25px 20px;
      }

      .stat-content h3 {
        font-size: 2rem;
      }

      .action-grid {
        grid-template-columns: 1fr;
        gap: 20px;
      }

      .action-card {
        padding: 25px 20px;
      }

      .security-events-grid,
      .high-risk-users-grid {
        grid-template-columns: 1fr;
        gap: 20px;
      }

      .security-event-card,
      .user-threat-card {
        padding: 25px 20px;
      }

      .user-stats {
        grid-template-columns: 1fr;
        gap: 15px;
      }

      .stat-item {
        padding: 12px 8px;
      }

      .event-actions,
      .user-actions {
        flex-direction: column;
        gap: 12px;
      }

      .action-details {
        flex-direction: column;
        gap: 8px;
      }

      .dashboard-section {
        padding: 25px 20px;
        margin-bottom: 40px;
      }

      .dashboard-section h2 {
        font-size: 1.6rem;
        margin-bottom: 25px;
      }

      .action-card-mini {
        padding: 20px 15px;
      }

      .no-events-message {
        padding: 3rem 1.5rem;
      }

      .no-events-icon {
        font-size: 3rem;
      }

      .no-events-message h3 {
        font-size: 1.3rem;
      }

      .btn {
        padding: 10px 20px;
        font-size: 0.9rem;
      }
    }

    @media (max-width: 480px) {
      .dashboard-welcome h1 {
        font-size: 1.8rem;
      }

      .stat-content h3 {
        font-size: 1.8rem;
      }

      .stat-icon {
        font-size: 2.5rem;
      }

      .action-icon {
        font-size: 2.5rem;
      }

      .dashboard-section {
        padding: 20px 15px;
      }

      .dashboard-section h2 {
        font-size: 1.4rem;
      }

      .security-event-card,
      .user-threat-card,
      .action-card-mini {
        padding: 20px 15px;
      }
    }
</style>
{% endblock %}

{% block extra_js %}
<!-- Particles.js script -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize particles.js
    particlesJS('particles-js', {
      "particles": {
        "number": {
          "value": 50,
          "density": {
            "enable": true,
            "value_area": 800
          }
        },
        "color": {
          "value": ["#C0ff6b", "#a0e066"]
        },
        "shape": {
          "type": "circle"
        },
        "opacity": {
          "value": 0.5,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 1,
            "opacity_min": 0.1,
            "sync": false
          }
        },
        "size": {
          "value": 3,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 2,
            "size_min": 0.1,
            "sync": false
          }
        },
        "line_linked": {
          "enable": true,
          "distance": 150,
          "color": "#C0ff6b",
          "opacity": 0.4,
          "width": 1
        },
        "move": {
          "enable": true,
          "speed": 1,
          "direction": "none",
          "random": true,
          "straight": false,
          "out_mode": "out",
          "bounce": false
        }
      },
      "interactivity": {
        "detect_on": "canvas",
        "events": {
          "onhover": {
            "enable": true,
            "mode": "grab"
          },
          "onclick": {
            "enable": true,
            "mode": "push"
          },
          "resize": true
        }
      },
      "retina_detect": true
    });

    // Animation for elements
    function revealElements() {
      const elements = document.querySelectorAll('.fade-in, .slide-right, .slide-left');
      elements.forEach(element => {
        element.classList.add('visible');
      });
    }

    // Run animations
    setTimeout(revealElements, 100);
  });
</script>

<script>
// Warning templates data
const warningTemplates = {{ warning_templates|safe }};

function showWarningModal(userId, securityLogId = null) {
    document.getElementById('warning-user-id').value = userId;
    document.getElementById('warning-security-log-id').value = securityLogId || '';
    document.getElementById('warning-template').value = '';
    document.getElementById('warning-title').value = '';
    document.getElementById('warning-message').value = '';
    document.getElementById('warning-severity').value = 'warning';

    new bootstrap.Modal(document.getElementById('warningModal')).show();
}

function showBlockModal(ipAddress, userId = null, securityLogIds = []) {
    document.getElementById('block-ip').value = ipAddress;
    document.getElementById('block-user-id').value = userId || '';
    document.getElementById('block-security-log-ids').value = JSON.stringify(securityLogIds);
    document.getElementById('block-type').value = 'temporary';
    document.getElementById('block-duration').value = '24';
    document.getElementById('block-reason').value = 'security_violation';
    document.getElementById('block-custom-reason').value = '';

    new bootstrap.Modal(document.getElementById('blockModal')).show();
}

function loadTemplate() {
    const templateKey = document.getElementById('warning-template').value;
    if (templateKey && warningTemplates[templateKey]) {
        const template = warningTemplates[templateKey];
        document.getElementById('warning-title').value = template.title;
        document.getElementById('warning-message').value = template.message;
        document.getElementById('warning-severity').value = template.severity;
    }
}

function toggleDuration() {
    const blockType = document.getElementById('block-type').value;
    const durationField = document.getElementById('duration-field');
    durationField.style.display = blockType === 'temporary' ? 'block' : 'none';
}

function sendWarning() {
    const formData = {
        user_id: document.getElementById('warning-user-id').value,
        template: document.getElementById('warning-template').value,
        custom_title: document.getElementById('warning-title').value,
        custom_message: document.getElementById('warning-message').value,
        severity: document.getElementById('warning-severity').value,
        security_log_id: document.getElementById('warning-security-log-id').value || null
    };

    fetch('{% url "accounts:api_send_warning" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || ''
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Warning sent successfully!');
            bootstrap.Modal.getInstance(document.getElementById('warningModal')).hide();
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        alert('Error sending warning: ' + error.message);
    });
}

function blockIP() {
    const formData = {
        ip_address: document.getElementById('block-ip').value,
        block_type: document.getElementById('block-type').value,
        duration_hours: parseInt(document.getElementById('block-duration').value),
        reason: document.getElementById('block-reason').value,
        custom_reason: document.getElementById('block-custom-reason').value,
        user_id: document.getElementById('block-user-id').value || null,
        security_log_ids: JSON.parse(document.getElementById('block-security-log-ids').value || '[]')
    };

    fetch('{% url "accounts:api_block_ip" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || ''
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('IP blocked successfully!');
            bootstrap.Modal.getInstance(document.getElementById('blockModal')).hide();
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        alert('Error blocking IP: ' + error.message);
    });
}

function showBulkActions() {
    // Show a more user-friendly notification
    if (typeof Toastify !== 'undefined') {
        Toastify({
            text: "🔧 Bulk Actions feature coming soon! Stay tuned for enhanced security management capabilities.",
            duration: 4000,
            gravity: "top",
            position: "right",
            backgroundColor: "linear-gradient(135deg, var(--color-border), #a8e063)",
            stopOnFocus: true
        }).showToast();
    } else {
        alert('Bulk actions feature coming soon!');
    }
}

// Auto-refresh dashboard every 2 minutes (less aggressive)
setInterval(() => {
    // Only refresh if user is not actively interacting
    if (document.hidden === false) {
        location.reload();
    }
}, 120000);
</script>
{% endblock %}
