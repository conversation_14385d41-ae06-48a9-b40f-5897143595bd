/**
 * Advanced Security Bundle for ForgeX
 * Comprehensive protection against DevTools, console access, and source viewing
 */

(function() {
    'use strict';
    
    // Check if user is admin from global config
    const isAdmin = window.FORGEX_CONFIG && window.FORGEX_CONFIG.debug_mode;
    
    if (isAdmin) {
        console.log('🔓 Advanced security disabled for admin user');
        return;
    }
    
    console.log('🛡️ Advanced security system loading...');
    
    // IMMEDIATE PROTECTION - Start right away
    initImmediateProtection();
    
    function initImmediateProtection() {
        // 1. AGGRESSIVE CONSOLE BLOCKING
        blockConsoleCompletely();
        
        // 2. DEVTOOLS DETECTION AND BLOCKING
        blockDevToolsAccess();
        
        // 3. SOURCE CODE PROTECTION
        protectSourceCode();
        
        // 4. KEYBOARD SHORTCUTS BLOCKING
        blockAllShortcuts();
        
        // 5. RIGHT-CLICK BLOCKING
        blockRightClick();
        
        console.log('🛡️ Advanced protection active');
    }
    
    // 1. COMPLETE CONSOLE BLOCKING
    function blockConsoleCompletely() {
        // Override all console methods
        const noop = function() { return undefined; };
        const consoleMethods = [
            'assert', 'clear', 'count', 'countReset', 'debug', 'dir', 'dirxml',
            'error', 'exception', 'group', 'groupCollapsed', 'groupEnd',
            'info', 'log', 'profile', 'profileEnd', 'table', 'time',
            'timeEnd', 'timeLog', 'timeStamp', 'trace', 'warn'
        ];
        
        // Create fake console
        const fakeConsole = {};
        consoleMethods.forEach(method => {
            fakeConsole[method] = noop;
        });
        
        // Replace console completely
        try {
            Object.defineProperty(window, 'console', {
                value: fakeConsole,
                writable: false,
                configurable: false
            });
        } catch(e) {
            window.console = fakeConsole;
        }
        
        // Block eval and Function constructor
        try {
            window.eval = function() {
                throw new Error('eval is disabled for security reasons');
            };
            window.Function = function() {
                throw new Error('Function constructor is disabled for security reasons');
            };
        } catch(e) {}
    }
    
    // 2. DEVTOOLS DETECTION AND BLOCKING
    function blockDevToolsAccess() {
        let devtoolsOpen = false;
        
        // Method 1: Console detection
        const detectConsole = () => {
            const element = new Image();
            Object.defineProperty(element, 'id', {
                get: function() {
                    devtoolsOpen = true;
                    handleDevToolsDetection();
                    return 'devtools-detected';
                }
            });
            console.log(element);
        };
        
        // Method 2: Window size detection
        const detectWindowSize = () => {
            const threshold = 160;
            if (window.outerHeight - window.innerHeight > threshold || 
                window.outerWidth - window.innerWidth > threshold) {
                if (!devtoolsOpen) {
                    devtoolsOpen = true;
                    handleDevToolsDetection();
                }
            } else {
                devtoolsOpen = false;
            }
        };
        
        // Method 3: Timing-based detection
        const detectTiming = () => {
            const start = performance.now();
            debugger;
            const end = performance.now();
            
            if (end - start > 100) {
                if (!devtoolsOpen) {
                    devtoolsOpen = true;
                    handleDevToolsDetection();
                }
            }
        };
        
        // Start monitoring
        setInterval(detectConsole, 2000);
        setInterval(detectWindowSize, 1000);
        setInterval(detectTiming, 3000);
        
        // Monitor window resize
        window.addEventListener('resize', detectWindowSize);
    }
    
    // Handle DevTools detection
    function handleDevToolsDetection() {
        // Only blur if DevTools are actually open (not just a false positive)
        setTimeout(() => {
            // Double-check if DevTools are really open
            const threshold = 160;
            if (window.outerHeight - window.innerHeight > threshold ||
                window.outerWidth - window.innerWidth > threshold) {

                // Blur the page but keep input fields functional
                document.body.style.filter = 'blur(10px)';
                // Don't disable pointer events completely - this breaks input fields
                // document.body.style.pointerEvents = 'none';
                document.body.style.userSelect = 'none';
            }
        }, 500); // Wait a bit to avoid false positives
        
        // Create warning overlay
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.95);
            color: #ff4444;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 999999;
            font-family: Arial, sans-serif;
            text-align: center;
        `;
        
        overlay.innerHTML = `
            <div style="max-width: 500px; padding: 40px;">
                <h1 style="color: #ff4444; margin-bottom: 20px; font-size: 32px;">🚫 ACCESS DENIED</h1>
                <p style="font-size: 18px; margin-bottom: 20px;">Developer tools have been detected!</p>
                <p style="font-size: 16px; color: #ccc; margin-bottom: 30px;">
                    This page is protected for security reasons. Please close developer tools to continue.
                </p>
                <p style="font-size: 14px; color: #888;">
                    This incident has been logged for security purposes.
                </p>
                <button onclick="window.location.reload()" style="
                    background: #ff4444;
                    color: white;
                    border: none;
                    padding: 12px 24px;
                    border-radius: 5px;
                    cursor: pointer;
                    font-size: 16px;
                    margin-top: 20px;
                ">Reload Page</button>
            </div>
        `;
        
        document.body.appendChild(overlay);
        
        // Log the security event
        logSecurityEvent('devtools_detected', 'Developer tools access detected and blocked');
    }
    
    // 3. SOURCE CODE PROTECTION (IMPROVED - ALLOWS TEXT READING)
    function protectSourceCode() {
        // Smart text selection - allow reading content but protect sensitive elements
        document.addEventListener('selectstart', (e) => {
            // Allow selection in content areas and interactive elements
            if (e.target.tagName === 'INPUT' ||
                e.target.tagName === 'TEXTAREA' ||
                e.target.tagName === 'SELECT' ||
                e.target.contentEditable === 'true' ||
                e.target.closest('input') ||
                e.target.closest('textarea') ||
                e.target.closest('.content') ||
                e.target.closest('.text-content') ||
                e.target.closest('p') ||
                e.target.closest('h1') ||
                e.target.closest('h2') ||
                e.target.closest('h3') ||
                e.target.closest('h4') ||
                e.target.closest('h5') ||
                e.target.closest('h6') ||
                e.target.closest('span') ||
                e.target.closest('div') ||
                e.target.closest('article') ||
                e.target.closest('.card-body') ||
                e.target.closest('.description')) {
                return true; // Allow selection for reading
            }

            // Block selection only on security-sensitive elements
            if (e.target.tagName === 'SCRIPT' ||
                e.target.tagName === 'STYLE' ||
                e.target.closest('script') ||
                e.target.closest('style') ||
                e.target.closest('.security-protected')) {
                e.preventDefault();
                return false;
            }

            // Allow normal text selection by default
            return true;
        });

        // Smart mouse interaction - allow normal interactions while protecting sensitive elements
        document.addEventListener('mousedown', (e) => {
            // Allow mouse events on interactive elements and content areas
            if (e.target.tagName === 'INPUT' ||
                e.target.tagName === 'TEXTAREA' ||
                e.target.tagName === 'BUTTON' ||
                e.target.tagName === 'SELECT' ||
                e.target.tagName === 'A' ||
                e.target.contentEditable === 'true' ||
                e.target.closest('input') ||
                e.target.closest('textarea') ||
                e.target.closest('button') ||
                e.target.closest('select') ||
                e.target.closest('a') ||
                e.target.closest('[role="button"]') ||
                e.target.closest('.clickable') ||
                e.target.closest('.content') ||
                e.target.closest('.text-content') ||
                e.target.closest('p') ||
                e.target.closest('h1') ||
                e.target.closest('h2') ||
                e.target.closest('h3') ||
                e.target.closest('h4') ||
                e.target.closest('h5') ||
                e.target.closest('h6') ||
                e.target.closest('span') ||
                e.target.closest('div') ||
                e.target.closest('article') ||
                e.target.closest('.card-body') ||
                e.target.closest('.description')) {
                return true; // Allow interaction
            }

            // Block interaction only on security-sensitive elements
            if (e.target.tagName === 'SCRIPT' ||
                e.target.tagName === 'STYLE' ||
                e.target.closest('script') ||
                e.target.closest('style') ||
                e.target.closest('.security-protected')) {
                e.preventDefault();
                return false;
            }

            // Allow normal interactions by default
            return true;
        });

        // CSS-based selection control - Allow text reading while protecting sensitive elements
        const style = document.createElement('style');
        style.textContent = `
            /* Allow text selection for normal content reading */
            body, p, h1, h2, h3, h4, h5, h6, span, div, article, section,
            .content, .text-content, .card-body, .description, .readable {
                -webkit-user-select: text !important;
                -moz-user-select: text !important;
                -ms-user-select: text !important;
                user-select: text !important;
            }

            /* Allow selection in input fields and interactive elements */
            input, textarea, select, button, a, [contenteditable="true"], .clickable {
                -webkit-user-select: text !important;
                -moz-user-select: text !important;
                -ms-user-select: text !important;
                user-select: text !important;
                pointer-events: auto !important;
            }

            /* Ensure input fields remain functional */
            input[type="text"], input[type="email"], input[type="password"],
            input[type="number"], input[type="search"], textarea {
                -webkit-user-select: text !important;
                -moz-user-select: text !important;
                -ms-user-select: text !important;
                user-select: text !important;
            }

            /* Block selection only on security-sensitive elements */
            script, style, .security-protected {
                -webkit-user-select: none !important;
                -moz-user-select: none !important;
                -ms-user-select: none !important;
                user-select: none !important;
                -webkit-touch-callout: none !important;
                -webkit-tap-highlight-color: transparent !important;
            }
        `;
        document.head.appendChild(style);

        // Block drag and drop only for non-input elements
        document.addEventListener('dragstart', (e) => {
            if (e.target.tagName !== 'INPUT' &&
                e.target.tagName !== 'TEXTAREA' &&
                !e.target.closest('input') &&
                !e.target.closest('textarea')) {
                e.preventDefault();
                return false;
            }
        });

        // Block printing
        window.addEventListener('beforeprint', (e) => {
            e.preventDefault();
            showSecurityAlert('Printing is disabled for security');
            return false;
        });
    }
    
    // 4. SELECTIVE KEYBOARD SHORTCUTS BLOCKING
    function blockAllShortcuts() {
        document.addEventListener('keydown', (e) => {
            const keyCode = e.keyCode || e.which;
            const ctrl = e.ctrlKey;
            const shift = e.shiftKey;

            // Allow normal typing in input fields
            if (e.target.tagName === 'INPUT' ||
                e.target.tagName === 'TEXTAREA' ||
                e.target.contentEditable === 'true') {

                // Only block specific dangerous shortcuts even in input fields
                if (keyCode === 123 || // F12
                    (ctrl && shift && keyCode === 73) || // Ctrl+Shift+I
                    (ctrl && shift && keyCode === 74) || // Ctrl+Shift+J
                    (ctrl && keyCode === 85)) { // Ctrl+U
                    e.preventDefault();
                    e.stopPropagation();
                    showSecurityAlert('Developer tools access denied');
                    return false;
                }

                // Allow other shortcuts in input fields (like Ctrl+A, Ctrl+C, Ctrl+V)
                return true;
            }

            // Block F12 (DevTools)
            if (keyCode === 123) {
                e.preventDefault();
                e.stopPropagation();
                showSecurityAlert('Developer tools access denied');
                return false;
            }

            // Block Ctrl+Shift+I (DevTools)
            if (ctrl && shift && keyCode === 73) {
                e.preventDefault();
                e.stopPropagation();
                showSecurityAlert('Developer tools access denied');
                return false;
            }

            // Block Ctrl+Shift+J (Console)
            if (ctrl && shift && keyCode === 74) {
                e.preventDefault();
                e.stopPropagation();
                showSecurityAlert('Console access denied');
                return false;
            }

            // Block Ctrl+U (View Source)
            if (ctrl && keyCode === 85) {
                e.preventDefault();
                e.stopPropagation();
                showSecurityAlert('View source access denied');
                return false;
            }

            // Block Ctrl+Shift+C (Element Inspector)
            if (ctrl && shift && keyCode === 67) {
                e.preventDefault();
                e.stopPropagation();
                showSecurityAlert('Element inspector access denied');
                return false;
            }

            // Block Ctrl+A (Select All) only outside input fields
            if (ctrl && keyCode === 65) {
                e.preventDefault();
                e.stopPropagation();
                return false;
            }

            // Block Ctrl+S (Save Page)
            if (ctrl && keyCode === 83) {
                e.preventDefault();
                e.stopPropagation();
                showSecurityAlert('Save page access denied');
                return false;
            }

            // Block Ctrl+P (Print)
            if (ctrl && keyCode === 80) {
                e.preventDefault();
                e.stopPropagation();
                showSecurityAlert('Print access denied');
                return false;
            }
        });
    }
    
    // 5. SELECTIVE RIGHT-CLICK BLOCKING
    function blockRightClick() {
        document.addEventListener('contextmenu', (e) => {
            // Allow right-click on input fields for paste/copy functionality
            if (e.target.tagName === 'INPUT' ||
                e.target.tagName === 'TEXTAREA' ||
                e.target.contentEditable === 'true') {
                return true; // Allow context menu on input fields
            }

            e.preventDefault();
            e.stopPropagation();
            showSecurityAlert('Right-click is disabled for security');
            return false;
        });
    }
    
    // Show security alert
    function showSecurityAlert(message) {
        const alertDiv = document.createElement('div');
        alertDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ff4444;
            color: white;
            padding: 15px 20px;
            border-radius: 5px;
            z-index: 999999;
            font-family: Arial, sans-serif;
            font-size: 14px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        `;
        
        alertDiv.innerHTML = `
            <div style="display: flex; align-items: center;">
                <span style="margin-right: 10px;">🚫</span>
                <span>${message}</span>
            </div>
        `;
        
        document.body.appendChild(alertDiv);
        
        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (alertDiv.parentElement) {
                alertDiv.remove();
            }
        }, 3000);
        
        // Log security event
        logSecurityEvent('blocked_action', message);
    }
    
    // Log security events
    function logSecurityEvent(eventType, details) {
        if (window.fetch) {
            // Get CSRF token from meta tag or cookie
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value ||
                             document.querySelector('meta[name=csrf-token]')?.content ||
                             getCookie('csrftoken') || '';

            fetch('/accounts/api/security-log/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify({
                    event_type: eventType,
                    details: details,
                    user_agent: navigator.userAgent,
                    timestamp: new Date().toISOString(),
                    url: window.location.href,
                    page_title: document.title,
                    referrer: document.referrer
                })
            }).then(response => {
                if (response.ok) {
                    console.log(`🔒 Security event logged: ${eventType}`);
                    return response.json();
                } else {
                    console.warn(`⚠️ Failed to log security event: ${response.status}`);
                    return response.text().then(text => {
                        console.warn(`⚠️ Error response:`, text);
                    });
                }
            }).then(data => {
                if (data && data.log_id) {
                    console.log(`✅ Security log created with ID: ${data.log_id}`);
                }
            }).catch(error => {
                console.warn(`⚠️ Security logging error:`, error);
            });
        }
    }

    // Helper function to get cookie value
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
    
    // Export security status and test functions
    window.ForgeXSecurity = {
        enabled: true,
        adminMode: false,
        version: '2.0.0',
        status: 'active',
        testLogging: function() {
            console.log('🧪 Testing security logging...');
            logSecurityEvent('devtools_detected', 'Manual test of security logging system');
        }
    };

    // Check for pending security warnings
    function checkPendingWarnings() {
        fetch('/accounts/api/pending-warnings/', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken') || ''
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.warnings && data.warnings.length > 0) {
                showSecurityWarnings(data.warnings);
            }
        })
        .catch(error => {
            console.warn('Failed to check pending warnings:', error);
        });
    }

    // Show security warnings as modals
    function showSecurityWarnings(warnings) {
        warnings.forEach((warning, index) => {
            setTimeout(() => {
                showWarningModal(warning);
            }, index * 1000); // Show warnings with 1 second delay between them
        });
    }

    // Show individual warning modal
    function showWarningModal(warning) {
        // Create modal HTML
        const modalHtml = `
            <div class="modal fade security-warning-modal" id="securityWarning${warning.id}" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content border-0">
                        <div class="modal-header bg-${getSeverityColor(warning.severity)} text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Security Warning
                            </h5>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-${getSeverityColor(warning.severity)} border-0">
                                <h6 class="alert-heading">${warning.title}</h6>
                                <p class="mb-0">${warning.message.replace(/\n/g, '<br>')}</p>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <strong>Severity:</strong> ${warning.severity_display}
                                    </small>
                                </div>
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <strong>Date:</strong> ${new Date(warning.created_at).toLocaleString()}
                                    </small>
                                </div>
                            </div>
                            <div class="mt-3 p-3 bg-light rounded">
                                <h6><i class="fas fa-info-circle text-info"></i> What should I do?</h6>
                                <ul class="mb-0">
                                    <li>Review and understand the security policy violation</li>
                                    <li>Avoid repeating the actions that triggered this warning</li>
                                    <li>Contact support if you have questions</li>
                                    <li>Acknowledge this warning to continue using the platform</li>
                                </ul>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <a href="/accounts/warnings/" class="btn btn-outline-secondary">
                                <i class="fas fa-list"></i> View All Warnings
                            </a>
                            <button type="button" class="btn btn-primary" onclick="acknowledgeWarning(${warning.id})">
                                <i class="fas fa-check"></i> I Understand
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById(`securityWarning${warning.id}`));
        modal.show();

        // Mark as delivered
        markWarningDelivered(warning.id);
    }

    // Get severity color for styling
    function getSeverityColor(severity) {
        switch(severity) {
            case 'critical': return 'danger';
            case 'final_warning': return 'warning';
            default: return 'info';
        }
    }

    // Acknowledge warning
    window.acknowledgeWarning = function(warningId) {
        fetch(`/accounts/warnings/acknowledge/${warningId}/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken') || ''
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById(`securityWarning${warningId}`));
                if (modal) {
                    modal.hide();
                }

                // Remove modal from DOM
                setTimeout(() => {
                    const modalElement = document.getElementById(`securityWarning${warningId}`);
                    if (modalElement) {
                        modalElement.remove();
                    }
                }, 500);

                console.log('✅ Security warning acknowledged');
            } else {
                alert('Error acknowledging warning: ' + data.error);
            }
        })
        .catch(error => {
            alert('Error: ' + error.message);
        });
    };

    // Mark warning as delivered
    function markWarningDelivered(warningId) {
        fetch(`/accounts/api/mark-warning-delivered/${warningId}/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken') || ''
            }
        }).catch(() => {}); // Silent fail
    }

    // Check for warnings when page loads
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', checkPendingWarnings);
    } else {
        checkPendingWarnings();
    }

    // Log that security system is active
    console.log('🛡️ Advanced Security System Active');
    console.log('🧪 Test security logging with: ForgeXSecurity.testLogging()');

})();
