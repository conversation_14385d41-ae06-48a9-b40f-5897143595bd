/**
 * Source Code Protection for ForgeX
 * Prevents viewing page source and accessing developer tools
 */

(function() {
    'use strict';
    
    // Check if user is admin
    const isAdmin = window.FORGEX_CONFIG && window.FORGEX_CONFIG.debug_mode;
    
    if (isAdmin) {
        console.log('🔓 Source protection disabled for admin user');
        return;
    }
    
    // Smart text selection control - Allow reading but prevent source code access
    const disableSelection = () => {
        // Remove the aggressive global blocking - this was preventing normal text selection
        // Instead, use selective blocking for security-sensitive elements only

        document.addEventListener('selectstart', (e) => {
            // Allow selection in content areas, input fields, and interactive elements
            if (e.target.tagName === 'INPUT' ||
                e.target.tagName === 'TEXTAREA' ||
                e.target.tagName === 'SELECT' ||
                e.target.contentEditable === 'true' ||
                e.target.closest('input') ||
                e.target.closest('textarea') ||
                e.target.closest('.content') ||
                e.target.closest('.text-content') ||
                e.target.closest('p') ||
                e.target.closest('h1') ||
                e.target.closest('h2') ||
                e.target.closest('h3') ||
                e.target.closest('h4') ||
                e.target.closest('h5') ||
                e.target.closest('h6') ||
                e.target.closest('span') ||
                e.target.closest('div.readable') ||
                e.target.closest('article') ||
                e.target.closest('.card-body') ||
                e.target.closest('.description')) {
                return true; // Allow selection for reading
            }

            // Block selection on security-sensitive elements
            if (e.target.tagName === 'SCRIPT' ||
                e.target.tagName === 'STYLE' ||
                e.target.closest('script') ||
                e.target.closest('style') ||
                e.target.closest('.security-protected')) {
                e.preventDefault();
                return false;
            }

            // Allow normal text selection for reading content
            return true;
        });

        // CSS-based selection control - Allow text selection for reading
        const style = document.createElement('style');
        style.textContent = `
            /* Allow text selection for normal content */
            body, p, h1, h2, h3, h4, h5, h6, span, div, article, section,
            .content, .text-content, .card-body, .description, .readable {
                -webkit-user-select: text !important;
                -moz-user-select: text !important;
                -ms-user-select: text !important;
                user-select: text !important;
            }

            /* Allow selection in input fields and interactive elements */
            input, textarea, select, button, a, [contenteditable="true"], .clickable {
                -webkit-user-select: text !important;
                -moz-user-select: text !important;
                -ms-user-select: text !important;
                user-select: text !important;
                pointer-events: auto !important;
            }

            /* Block selection only on security-sensitive elements */
            script, style, .security-protected {
                -webkit-user-select: none !important;
                -moz-user-select: none !important;
                -ms-user-select: none !important;
                user-select: none !important;
                -webkit-touch-callout: none !important;
                -webkit-tap-highlight-color: transparent !important;
            }
        `;
        document.head.appendChild(style);
    };
    
    // Block keyboard shortcuts
    const blockShortcuts = () => {
        document.addEventListener('keydown', (e) => {
            const key = e.keyCode || e.which;
            const ctrl = e.ctrlKey;
            const shift = e.shiftKey;
            const alt = e.altKey;
            
            // Block F12 (DevTools)
            if (key === 123) {
                e.preventDefault();
                e.stopPropagation();
                showSecurityAlert('Developer tools access denied');
                return false;
            }
            
            // Block Ctrl+Shift+I (DevTools)
            if (ctrl && shift && key === 73) {
                e.preventDefault();
                e.stopPropagation();
                showSecurityAlert('Developer tools access denied');
                return false;
            }
            
            // Block Ctrl+Shift+J (Console)
            if (ctrl && shift && key === 74) {
                e.preventDefault();
                e.stopPropagation();
                showSecurityAlert('Console access denied');
                return false;
            }
            
            // Block Ctrl+U (View Source)
            if (ctrl && key === 85) {
                e.preventDefault();
                e.stopPropagation();
                showSecurityAlert('View source access denied');
                return false;
            }
            
            // Block Ctrl+Shift+C (Element Inspector)
            if (ctrl && shift && key === 67) {
                e.preventDefault();
                e.stopPropagation();
                showSecurityAlert('Element inspector access denied');
                return false;
            }
            
            // Block Ctrl+A (Select All)
            if (ctrl && key === 65) {
                e.preventDefault();
                e.stopPropagation();
                return false;
            }
            
            // Block Ctrl+S (Save Page)
            if (ctrl && key === 83) {
                e.preventDefault();
                e.stopPropagation();
                showSecurityAlert('Save page access denied');
                return false;
            }
            
            // Block Ctrl+P (Print)
            if (ctrl && key === 80) {
                e.preventDefault();
                e.stopPropagation();
                showSecurityAlert('Print access denied');
                return false;
            }
            
            // Block F5 and Ctrl+R (Refresh) - Optional
            // if (key === 116 || (ctrl && key === 82)) {
            //     e.preventDefault();
            //     return false;
            // }
        });
    };
    
    // Block right-click context menu
    const blockContextMenu = () => {
        document.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            e.stopPropagation();
            showSecurityAlert('Right-click is disabled for security');
            return false;
        });
        
        // Block on images specifically
        document.addEventListener('dragstart', (e) => {
            if (e.target.tagName === 'IMG') {
                e.preventDefault();
                return false;
            }
        });
    };
    
    // Show security alert
    const showSecurityAlert = (message) => {
        // Create alert overlay
        const alertDiv = document.createElement('div');
        alertDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ff4444;
            color: white;
            padding: 15px 20px;
            border-radius: 5px;
            z-index: 999999;
            font-family: Arial, sans-serif;
            font-size: 14px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            animation: slideIn 0.3s ease-out;
        `;
        
        alertDiv.innerHTML = `
            <div style="display: flex; align-items: center;">
                <span style="margin-right: 10px;">🚫</span>
                <span>${message}</span>
            </div>
        `;
        
        // Add animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
        
        document.body.appendChild(alertDiv);
        
        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (alertDiv.parentElement) {
                alertDiv.style.animation = 'slideIn 0.3s ease-out reverse';
                setTimeout(() => alertDiv.remove(), 300);
            }
        }, 3000);
        
        // Log security event
        logSecurityEvent('blocked_action', message);
    };
    
    // Log security events
    const logSecurityEvent = (eventType, details) => {
        if (window.fetch) {
            fetch('/accounts/api/security/log/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrf-token]')?.content || ''
                },
                body: JSON.stringify({
                    event_type: eventType,
                    details: details,
                    user_agent: navigator.userAgent,
                    timestamp: new Date().toISOString(),
                    url: window.location.href
                })
            }).catch(() => {});
        }
    };
    
    // Detect DevTools by console
    const detectDevTools = () => {
        let devtools = false;
        const element = new Image();
        
        Object.defineProperty(element, 'id', {
            get: function() {
                devtools = true;
                handleDevToolsDetection();
                return 'devtools-detected';
            }
        });
        
        console.log(element);
        return devtools;
    };
    
    // Handle DevTools detection
    const handleDevToolsDetection = () => {
        // Blur the entire page
        document.body.style.filter = 'blur(10px)';
        document.body.style.pointerEvents = 'none';
        
        // Create warning overlay
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.95);
            color: #ff4444;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 999999;
            font-family: Arial, sans-serif;
            text-align: center;
        `;
        
        overlay.innerHTML = `
            <div style="max-width: 500px; padding: 40px;">
                <h1 style="color: #ff4444; margin-bottom: 20px; font-size: 32px;">🚫 ACCESS DENIED</h1>
                <p style="font-size: 18px; margin-bottom: 20px;">Developer tools have been detected!</p>
                <p style="font-size: 16px; color: #ccc; margin-bottom: 30px;">
                    This page is protected for security reasons. Please close developer tools to continue.
                </p>
                <p style="font-size: 14px; color: #888;">
                    This incident has been logged for security purposes.
                </p>
                <button onclick="window.location.reload()" style="
                    background: #ff4444;
                    color: white;
                    border: none;
                    padding: 12px 24px;
                    border-radius: 5px;
                    cursor: pointer;
                    font-size: 16px;
                    margin-top: 20px;
                ">Reload Page</button>
            </div>
        `;
        
        document.body.appendChild(overlay);
        
        // Log the detection
        logSecurityEvent('devtools_detected', 'Developer tools access detected and blocked');
    };
    
    // Monitor for DevTools opening
    const monitorDevTools = () => {
        // Method 1: Console detection
        detectDevTools();
        
        // Method 2: Window size detection
        const threshold = 160;
        if (window.outerHeight - window.innerHeight > threshold || 
            window.outerWidth - window.innerWidth > threshold) {
            handleDevToolsDetection();
        }
    };
    
    // Initialize all protections
    const initSourceProtection = () => {
        disableSelection();
        blockShortcuts();
        blockContextMenu();
        
        // Start monitoring
        setInterval(monitorDevTools, 2000);
        
        // Monitor window resize
        window.addEventListener('resize', () => {
            setTimeout(monitorDevTools, 100);
        });
        
        // Block printing
        window.addEventListener('beforeprint', (e) => {
            e.preventDefault();
            showSecurityAlert('Printing is disabled for security');
            return false;
        });
        
        // Block save page
        window.addEventListener('beforeunload', (e) => {
            // This doesn't actually prevent saving, but we can log it
            logSecurityEvent('page_unload', 'User attempting to leave page');
        });
        
        console.log('🛡️ Source protection initialized');
    };
    
    // Start protection
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initSourceProtection);
    } else {
        initSourceProtection();
    }
    
})();
