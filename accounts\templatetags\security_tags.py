"""
Django template tags for security-enhanced JavaScript loading
"""

from django import template
from django.conf import settings
from django.utils.safestring import mark_safe
from django.contrib.auth.models import User
import hashlib
import json
import os

register = template.Library()

@register.simple_tag(takes_context=True)
def load_secure_js(context, bundle_name, **kwargs):
    """
    Load JavaScript bundles with security considerations
    """
    request = context.get('request')
    user = request.user if request else None

    # Determine security level based on user
    is_debug_user = False
    if user and user.is_authenticated:
        is_debug_user = (user.is_superuser or user.is_staff)

    # Load security manifest to get actual filenames
    manifest_path = os.path.join(settings.BASE_DIR, 'static', 'dist', 'security-manifest.json')

    try:
        if os.path.exists(manifest_path):
            with open(manifest_path, 'r') as f:
                manifest = json.load(f)
        else:
            manifest = {}
    except (json.JSONDecodeError, IOError):
        manifest = {}

    # Find the actual filename for the bundle
    bundle_filename = None
    if manifest.get('bundles'):
        # Look for files that start with the bundle name and end with .min.js
        for filename in manifest['bundles'].keys():
            # Extract the base name from the filename (before the hash)
            if '.' in filename:
                file_base = filename.split('.')[0]
                if file_base == bundle_name and filename.endswith('.min.js'):
                    bundle_filename = filename
                    break

    # Fallback to simple naming if not found in manifest
    if not bundle_filename:
        if is_debug_user:
            bundle_filename = f'{bundle_name}.bundle.js'
        else:
            bundle_filename = f'{bundle_name}.min.js'

    # Build the path
    bundle_path = f'/static/dist/{bundle_filename}'

    # Add cache busting
    cache_buster = get_cache_buster()
    bundle_path += f'?v={cache_buster}'

    # Generate script tag with security attributes
    script_attrs = {
        'src': bundle_path,
        'type': 'text/javascript',
        'defer': kwargs.get('defer', True),
    }

    # Add security headers for non-debug users
    if not is_debug_user:
        script_attrs['data-security'] = 'protected'

    # Build script tag
    attrs_str = ' '.join([f'{k}="{v}"' if v is not True else k for k, v in script_attrs.items() if v])
    script_tag = f'<script {attrs_str}></script>'

    return mark_safe(script_tag)

@register.simple_tag(takes_context=True)
def security_config(context):
    """
    Generate security configuration for client-side
    """
    request = context.get('request')
    user = request.user if request else None

    # Simple, safe configuration
    is_authenticated = user and user.is_authenticated
    is_admin = is_authenticated and (user.is_superuser or user.is_staff)

    # Create a simple config object with only basic types
    config = {
        'debug_mode': bool(is_admin),
        'obfuscation_enabled': bool(not is_admin),
        'encryption_enabled': True,
        'user_role': str('admin' if is_admin else ('user' if is_authenticated else 'anonymous')),
        'user_id': int(user.id) if is_authenticated else 0,
        'username': str(user.username) if is_authenticated else '',
        'api_base_url': '/api/',
        'websocket_url': 'ws://localhost:8001/',
    }

    # Convert to JSON safely with error handling
    try:
        config_json = json.dumps(config)
    except Exception:
        # Fallback minimal config
        config_json = '{"debug_mode": false, "user_role": "anonymous"}'

    return mark_safe(f'''
    <script type="text/javascript">
        window.FORGEX_CONFIG = {config_json};
        window.FORGEX_CONFIG.timestamp = Date.now();

        // Security initialization
        if (!window.FORGEX_CONFIG.debug_mode) {{
            // Basic security measures for non-admin users
            console.log('🛡️ Security system active');
        }} else {{
            console.log('🔓 Debug mode - Security relaxed for admin user');
        }}
    </script>
    ''')

@register.simple_tag(takes_context=True)
def load_security_bundle(context):
    """
    Load the enhanced security bundle with heavy obfuscation
    """
    request = context.get('request')
    user = request.user if request else None

    is_debug_user = False
    if user and user.is_authenticated:
        is_debug_user = (user.is_superuser or user.is_staff)

    cache_buster = get_cache_buster()

    if is_debug_user:
        # Load minimal security for debugging
        return mark_safe(f'''
        <script type="text/javascript" src="/static/js/security/reliable-security.js?v={cache_buster}" defer></script>
        <script type="text/javascript">
            console.log("🔓 Debug mode enabled - Loading reliable security only");
            window.FORGEX_CONFIG = window.FORGEX_CONFIG || {{}};
            window.FORGEX_CONFIG.debug_mode = true;
        </script>
        ''')
    else:
        # Load reliable security system for production
        return mark_safe(f'''
        <script type="text/javascript" src="/static/js/security/reliable-security.js?v={cache_buster}" defer></script>
        <script type="text/javascript">
            // Production security initialization
            window.FORGEX_CONFIG = window.FORGEX_CONFIG || {{}};
            window.FORGEX_CONFIG.debug_mode = false;
            console.log("🛡️ Production security loaded");
        </script>
        ''')

@register.simple_tag
def api_endpoint(endpoint_name):
    """
    Generate obfuscated API endpoints for production
    """
    # In production, this would return obfuscated endpoints
    # For now, return the original endpoint
    endpoint_map = {
        'user_profile': '/accounts/api/profile/',
        'projects': '/collaborate/api/projects/',
        'courses': '/learn/api/courses/',
        'mentors': '/mentorship/api/mentors/',
        'files': '/api/files/',
        'chat': '/api/chat/',
    }
    
    return endpoint_map.get(endpoint_name, f'/api/{endpoint_name}/')

@register.simple_tag(takes_context=True)
def security_headers(context):
    """
    Generate security-related meta tags and headers
    """
    request = context.get('request')
    
    # Generate nonce for CSP
    nonce = generate_nonce()
    
    # Store nonce in request for use in views
    if request:
        request.security_nonce = nonce
    
    # Get CSRF token safely
    csrf_token = ''
    if request and hasattr(request, 'META'):
        csrf_token = request.META.get('CSRF_COOKIE', '')

    headers = f'''
    <meta name="csrf-token" content="{csrf_token}">
    <meta name="security-nonce" content="{nonce}">
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    '''
    
    # Add CSP header
    csp_policy = (
        "default-src 'self'; "
        "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; "
        "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; "
        "font-src 'self' https://fonts.gstatic.com; "
        "img-src 'self' data: https:; "
        "connect-src 'self' ws: wss:; "
        f"script-src-elem 'self' 'nonce-{nonce}' https://cdnjs.cloudflare.com https://cdn.jsdelivr.net;"
    )
    
    headers += f'<meta http-equiv="Content-Security-Policy" content="{csp_policy}">'
    
    return mark_safe(headers)

@register.simple_tag(takes_context=True)
def conditional_script(context, condition, script_content):
    """
    Conditionally include script based on user permissions
    """
    request = context.get('request')
    user = request.user if request else None
    
    # Evaluate condition
    should_include = False
    
    if condition == 'debug':
        should_include = (user and user.is_authenticated and 
                         (user.is_superuser or user.is_staff))
    elif condition == 'admin':
        should_include = (user and user.is_superuser)
    elif condition == 'staff':
        should_include = (user and (user.is_superuser or user.is_staff))
    elif condition == 'authenticated':
        should_include = (user and user.is_authenticated)
    
    if should_include:
        nonce = getattr(request, 'security_nonce', generate_nonce())
        return mark_safe(f'<script nonce="{nonce}">{script_content}</script>')
    else:
        return ''

@register.filter
def obfuscate_for_user(value, user):
    """
    Obfuscate values based on user permissions
    """
    if not user or not user.is_authenticated:
        return '***'
    
    if user.is_superuser or user.is_staff:
        return value
    
    # Obfuscate for regular users
    if isinstance(value, str) and len(value) > 4:
        return value[:2] + '*' * (len(value) - 4) + value[-2:]
    
    return '***'

def generate_integrity_hash(file_path):
    """
    Generate SRI hash for JavaScript files
    """
    try:
        full_path = os.path.join(settings.STATIC_ROOT or settings.BASE_DIR, file_path.lstrip('/'))
        if os.path.exists(full_path):
            with open(full_path, 'rb') as f:
                content = f.read()
                hash_value = hashlib.sha384(content).digest()
                import base64
                return f"sha384-{base64.b64encode(hash_value).decode()}"
    except Exception:
        pass
    return ''

def generate_nonce():
    """
    Generate a random nonce for CSP
    """
    import secrets
    return secrets.token_urlsafe(16)

def get_cache_buster():
    """
    Generate cache buster based on file modification time or version
    """
    # In production, this could be based on deployment timestamp
    # For development, use current timestamp
    if getattr(settings, 'DEBUG', False):
        import time
        return str(int(time.time()))
    else:
        # Use a fixed version number in production
        return getattr(settings, 'STATIC_VERSION', '1.0.0')

@register.inclusion_tag('security/security_monitor.html', takes_context=True)
def security_monitor(context):
    """
    Include security monitoring widget for admin users
    """
    request = context.get('request')
    user = request.user if request else None
    
    show_monitor = (user and user.is_authenticated and 
                   (user.is_superuser or 
                    (hasattr(user, 'security_profile') and 
                     user.security_profile.can_modify_security_settings)))
    
    return {
        'show_monitor': show_monitor,
        'user': user,
        'request': request,
    }
