/**
 * Advanced Console Blocker for ForgeX
 * Completely disables console access for non-admin users
 */

(function() {
    'use strict';
    
    // Check if user is admin
    const isAdmin = window.FORGEX_CONFIG && window.FORGEX_CONFIG.debug_mode;
    
    if (isAdmin) {
        console.log('🔓 Console blocker disabled for admin user');
        return;
    }
    
    // Advanced console blocking
    const blockConsole = () => {
        // Store original console
        const originalConsole = window.console;
        
        // Create fake console object
        const fakeConsole = {};
        
        // List of all console methods
        const consoleMethods = [
            'assert', 'clear', 'count', 'countReset', 'debug', 'dir', 'dirxml',
            'error', 'exception', 'group', 'groupCollapsed', 'groupEnd',
            'info', 'log', 'profile', 'profileEnd', 'table', 'time',
            'timeEnd', 'timeLog', 'timeStamp', 'trace', 'warn'
        ];
        
        // Override each console method
        consoleMethods.forEach(method => {
            fakeConsole[method] = function() {
                // Log security violation
                if (window.fetch && Math.random() < 0.1) { // Only log 10% to avoid spam
                    fetch('/accounts/api/security/log/', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': document.querySelector('[name=csrf-token]')?.content || ''
                        },
                        body: JSON.stringify({
                            event_type: 'console_access_attempt',
                            details: `Attempted to use console.${method}`,
                            timestamp: new Date().toISOString()
                        })
                    }).catch(() => {});
                }
                
                // Return fake success
                return undefined;
            };
        });
        
        // Replace console
        try {
            Object.defineProperty(window, 'console', {
                value: fakeConsole,
                writable: false,
                configurable: false
            });
        } catch(e) {
            window.console = fakeConsole;
        }
        
        // Block console property access
        try {
            delete window.console;
            window.console = fakeConsole;
        } catch(e) {}
        
        // Override console in all frames
        try {
            if (window.frames) {
                for (let i = 0; i < window.frames.length; i++) {
                    try {
                        window.frames[i].console = fakeConsole;
                    } catch(e) {}
                }
            }
        } catch(e) {}
    };
    
    // Block eval and Function constructor
    const blockEval = () => {
        try {
            window.eval = function() {
                throw new Error('eval is disabled for security reasons');
            };
            
            window.Function = function() {
                throw new Error('Function constructor is disabled for security reasons');
            };
        } catch(e) {}
    };
    
    // Block common debugging methods
    const blockDebugging = () => {
        // Block debugger statement
        const originalDebugger = window.debugger;
        try {
            Object.defineProperty(window, 'debugger', {
                get: function() {
                    throw new Error('Debugger access denied');
                },
                set: function() {
                    throw new Error('Debugger access denied');
                }
            });
        } catch(e) {}
        
        // Block setTimeout/setInterval with strings (can be used for eval)
        const originalSetTimeout = window.setTimeout;
        const originalSetInterval = window.setInterval;
        
        window.setTimeout = function(code, delay) {
            if (typeof code === 'string') {
                throw new Error('String-based setTimeout is disabled for security reasons');
            }
            return originalSetTimeout.apply(this, arguments);
        };
        
        window.setInterval = function(code, delay) {
            if (typeof code === 'string') {
                throw new Error('String-based setInterval is disabled for security reasons');
            }
            return originalSetInterval.apply(this, arguments);
        };
    };
    
    // Detect and block console opening
    const detectConsoleOpening = () => {
        let devtools = false;
        
        // Method 1: Console object detection
        const img = new Image();
        Object.defineProperty(img, 'id', {
            get: function() {
                devtools = true;
                // Immediately blur the page
                document.body.style.filter = 'blur(20px)';
                document.body.style.pointerEvents = 'none';
                
                // Show warning
                setTimeout(() => {
                    alert('🚫 Developer console detected! Page access restricted for security.');
                    window.location.reload();
                }, 100);
                
                return 'console-detected';
            }
        });
        
        // Trigger detection
        console.log(img);
        console.clear();
        
        return devtools;
    };
    
    // Monitor for DevTools
    const monitorDevTools = () => {
        // Check window size changes (DevTools opening)
        const threshold = 160;
        if (window.outerHeight - window.innerHeight > threshold || 
            window.outerWidth - window.innerWidth > threshold) {
            
            // Blur page immediately
            document.body.style.filter = 'blur(15px)';
            document.body.style.userSelect = 'none';
            
            // Show overlay
            const overlay = document.createElement('div');
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.9);
                color: #ff4444;
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 999999;
                font-family: Arial, sans-serif;
                font-size: 20px;
                text-align: center;
            `;
            
            overlay.innerHTML = `
                <div>
                    <h1>🚫 Access Denied</h1>
                    <p>Developer tools are not allowed on this page.</p>
                    <p>Please close developer tools to continue.</p>
                </div>
            `;
            
            document.body.appendChild(overlay);
            
            // Check if DevTools closed
            const checkClosed = setInterval(() => {
                if (window.outerHeight - window.innerHeight <= threshold && 
                    window.outerWidth - window.innerWidth <= threshold) {
                    
                    document.body.style.filter = '';
                    document.body.style.userSelect = '';
                    document.body.style.pointerEvents = '';
                    
                    if (overlay.parentElement) {
                        overlay.remove();
                    }
                    
                    clearInterval(checkClosed);
                }
            }, 500);
        }
    };
    
    // Initialize all protections
    const initConsoleBlocker = () => {
        blockConsole();
        blockEval();
        blockDebugging();
        
        // Start monitoring
        setInterval(detectConsoleOpening, 2000);
        setInterval(monitorDevTools, 1000);
        
        // Monitor window resize
        window.addEventListener('resize', monitorDevTools);
        
        // Block drag operations on sensitive elements only (not text selection)
        document.addEventListener('dragstart', (e) => {
            if (!isAdmin) {
                // Only block dragging of images and other media, not text selection
                if (e.target.tagName === 'IMG' ||
                    e.target.tagName === 'VIDEO' ||
                    e.target.tagName === 'AUDIO' ||
                    e.target.closest('script') ||
                    e.target.closest('style')) {
                    e.preventDefault();
                    return false;
                }
            }
        });
    };
    
    // Start when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initConsoleBlocker);
    } else {
        initConsoleBlocker();
    }
    
    // Prevent this script from being modified
    Object.freeze(console);
    
})();
