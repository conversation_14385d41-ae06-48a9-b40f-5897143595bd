<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Text Selection Test - ForgeX Security</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        
        .test-section h2 {
            color: #333;
            margin-top: 0;
        }
        
        .success {
            border-color: #4CAF50;
            background: #f0fff0;
        }
        
        .warning {
            border-color: #ff9800;
            background: #fff8e1;
        }
        
        .error {
            border-color: #f44336;
            background: #ffebee;
        }
        
        .test-content {
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 4px;
            border: 1px solid #ccc;
        }
        
        .input-test {
            margin: 10px 0;
        }
        
        .input-test input, .input-test textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
            vertical-align: middle;
        }
        
        .status-pass {
            background: #4CAF50;
        }
        
        .status-fail {
            background: #f44336;
        }
        
        .status-unknown {
            background: #ccc;
        }
        
        .test-results {
            margin-top: 30px;
            padding: 20px;
            background: #e3f2fd;
            border-radius: 8px;
        }
        
        .security-protected {
            background: #ffcdd2;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🛡️ ForgeX Security - Text Selection Test</h1>
    <p>This page tests whether the security fixes allow normal text selection while maintaining protection.</p>
    
    <!-- Global Config for Security -->
    <script>
        window.FORGEX_CONFIG = {
            debug_mode: false, // Set to false to test security features
            timestamp: Date.now()
        };
    </script>
    
    <div class="test-section">
        <h2><span class="status-indicator status-unknown" id="status-normal-text"></span>Normal Text Selection</h2>
        <div class="test-content">
            <p>This is a normal paragraph. You should be able to select this text for reading purposes. Try selecting this text with your mouse to see if the text selection works properly.</p>
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.</p>
            <h3>This is a heading that should also be selectable</h3>
            <span>This is a span element that should allow text selection.</span>
        </div>
    </div>
    
    <div class="test-section">
        <h2><span class="status-indicator status-unknown" id="status-input-fields"></span>Input Field Functionality</h2>
        <div class="test-content">
            <div class="input-test">
                <label>Text Input:</label>
                <input type="text" placeholder="Type here and try to select text..." value="Test input field">
            </div>
            <div class="input-test">
                <label>Textarea:</label>
                <textarea placeholder="Type here and try to select text..." rows="3">This is a textarea with some content that you should be able to select and edit.</textarea>
            </div>
            <div class="input-test">
                <label>Select Dropdown:</label>
                <select>
                    <option>Option 1</option>
                    <option>Option 2</option>
                    <option>Option 3</option>
                </select>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2><span class="status-indicator status-unknown" id="status-content-areas"></span>Content Areas</h2>
        <div class="test-content content">
            <p>This div has the 'content' class and should allow text selection.</p>
        </div>
        <div class="test-content text-content">
            <p>This div has the 'text-content' class and should allow text selection.</p>
        </div>
        <div class="test-content card-body">
            <p>This div has the 'card-body' class and should allow text selection.</p>
        </div>
        <div class="test-content description">
            <p>This div has the 'description' class and should allow text selection.</p>
        </div>
    </div>
    
    <div class="test-section">
        <h2><span class="status-indicator status-unknown" id="status-security-protected"></span>Security Protected Elements</h2>
        <div class="test-content">
            <p>Normal text that should be selectable.</p>
            <div class="security-protected">
                This element has the 'security-protected' class and should NOT allow text selection for security reasons.
            </div>
            <p>More normal text that should be selectable.</p>
        </div>
    </div>
    
    <div class="test-results">
        <h2>🧪 Test Results</h2>
        <div id="test-results-content">
            <p>Perform the following tests manually:</p>
            <ol>
                <li><strong>Normal Text:</strong> Try to select text in the paragraphs and headings above</li>
                <li><strong>Input Fields:</strong> Try to select and edit text in the input fields</li>
                <li><strong>Content Areas:</strong> Try to select text in the different content area divs</li>
                <li><strong>Security Protected:</strong> Try to select text in the red security-protected area (should be blocked)</li>
            </ol>
            <p><strong>Expected Results:</strong></p>
            <ul>
                <li>✅ Normal text, headings, and content areas should allow selection</li>
                <li>✅ Input fields should work normally (typing, selecting, editing)</li>
                <li>❌ Security-protected elements should block text selection</li>
                <li>🛡️ Developer tools should still be blocked (F12, Ctrl+Shift+I, etc.)</li>
                <li>🛡️ Right-click context menu should still be blocked on non-input elements</li>
            </ul>
        </div>
    </div>
    
    <!-- Load Security Scripts -->
    <script>
        // Load advanced security for testing
        if (window.FORGEX_CONFIG && !window.FORGEX_CONFIG.debug_mode) {
            console.log('🛡️ Loading security scripts for testing...');
            
            // Load advanced security
            const advancedScript = document.createElement('script');
            advancedScript.src = '/static/js/security/advanced-security.js';
            advancedScript.async = false;
            document.head.appendChild(advancedScript);
            
            // Load source protection
            const sourceScript = document.createElement('script');
            sourceScript.src = '/static/js/security/source-protection.js';
            sourceScript.async = false;
            document.head.appendChild(sourceScript);
            
            console.log('🛡️ Security scripts loaded');
        } else {
            console.log('🔓 Debug mode - Security scripts not loaded');
        }
    </script>
    
    <!-- Test Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Text Selection Test Page Loaded');
            console.log('🛡️ Security Config:', window.FORGEX_CONFIG);
            
            // Auto-test some functionality
            setTimeout(function() {
                testTextSelection();
            }, 2000); // Wait for security scripts to load
        });
        
        function testTextSelection() {
            console.log('🧪 Running automated text selection tests...');
            
            // Test if we can programmatically select text
            try {
                const testParagraph = document.querySelector('.test-content p');
                if (testParagraph) {
                    const selection = window.getSelection();
                    const range = document.createRange();
                    range.selectNodeContents(testParagraph);
                    selection.removeAllRanges();
                    selection.addRange(range);
                    
                    if (selection.toString().length > 0) {
                        console.log('✅ Programmatic text selection works');
                        updateStatus('status-normal-text', true);
                    } else {
                        console.log('❌ Programmatic text selection blocked');
                        updateStatus('status-normal-text', false);
                    }
                    
                    // Clear selection
                    selection.removeAllRanges();
                }
            } catch (error) {
                console.log('❌ Text selection test error:', error);
                updateStatus('status-normal-text', false);
            }
            
            // Test input field functionality
            try {
                const testInput = document.querySelector('input[type="text"]');
                if (testInput) {
                    testInput.focus();
                    testInput.select();
                    
                    if (testInput.selectionStart !== testInput.selectionEnd) {
                        console.log('✅ Input field selection works');
                        updateStatus('status-input-fields', true);
                    } else {
                        console.log('❌ Input field selection blocked');
                        updateStatus('status-input-fields', false);
                    }
                }
            } catch (error) {
                console.log('❌ Input field test error:', error);
                updateStatus('status-input-fields', false);
            }
            
            console.log('🧪 Automated tests completed. Please perform manual tests as well.');
        }
        
        function updateStatus(elementId, passed) {
            const statusElement = document.getElementById(elementId);
            if (statusElement) {
                statusElement.className = 'status-indicator ' + (passed ? 'status-pass' : 'status-fail');
            }
        }
    </script>
</body>
</html>
