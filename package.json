{"dependencies": {"xterm": "^5.3.0", "y-monaco": "^0.1.6", "y-websocket": "^3.0.0", "yjs": "^13.6.27"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/preset-env": "^7.27.2", "babel-loader": "^9.2.1", "crypto-js": "^4.2.0", "css-loader": "^6.11.0", "javascript-obfuscator": "^4.1.1", "mini-css-extract-plugin": "^2.9.2", "style-loader": "^3.3.4", "terser-webpack-plugin": "^5.3.14", "webpack": "^5.99.9", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1", "webpack-obfuscator": "^3.5.1"}, "scripts": {"build": "webpack --mode production", "build-dev": "webpack --mode development", "watch": "webpack --mode development --watch", "serve": "webpack serve --mode development", "build-secure": "webpack --mode production --env secure=true", "build-admin": "webpack --mode production --env admin=true", "build-security": "node build-security.js", "build-security-max": "node build-security.js --level maximum", "build-all": "npm run build-security && npm run build-secure"}}