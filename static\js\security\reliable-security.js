/**
 * ForgeX Reliable Security System
 * Focused on preventing false positives while maintaining protection
 */

(function() {
    'use strict';
    
    // Check if user is admin
    const isAdmin = window.FORGEX_CONFIG && window.FORGEX_CONFIG.debug_mode;
    
    if (isAdmin) {
        console.log('🔓 Reliable security disabled for admin user');
        return;
    }
    
    console.log('🛡️ Reliable Security System Loading...');
    
    // Security state
    let securityState = {
        devtoolsDetected: false,
        violationCount: 0,
        lastDetection: 0,
        isBlocked: false
    };
    
    // Simple and reliable DevTools detection
    const DevToolsDetector = {
        // Method 1: Console detection (most reliable)
        detectConsole: function() {
            const element = new Image();
            let detected = false;
            
            Object.defineProperty(element, 'id', {
                get: function() {
                    detected = true;
                    return 'devtools-detected';
                }
            });
            
            console.log(element);
            console.clear();
            
            return detected;
        },
        
        // Method 2: Window size detection (conservative)
        detectWindowSize: function() {
            const heightDiff = window.outerHeight - window.innerHeight;
            const widthDiff = window.outerWidth - window.innerWidth;
            
            // Very conservative thresholds to prevent false positives
            return (heightDiff > 300 && widthDiff > 300);
        },
        
        // Main detection function
        runDetection: function() {
            const now = Date.now();
            
            // Throttle checks to prevent excessive detection
            if (now - securityState.lastDetection < 2000) return false;
            securityState.lastDetection = now;
            
            let detected = false;
            let methods = [];
            
            try {
                // Only use the most reliable methods
                if (this.detectConsole()) {
                    detected = true;
                    methods.push('console');
                }
                
                if (this.detectWindowSize()) {
                    detected = true;
                    methods.push('window_size');
                }
                
                if (detected && !securityState.devtoolsDetected) {
                    securityState.violationCount++;
                    
                    // Only trigger after multiple confirmations
                    if (securityState.violationCount >= 3) {
                        securityState.devtoolsDetected = true;
                        SecurityResponse.handleViolation(methods);
                    }
                } else if (!detected && securityState.devtoolsDetected) {
                    // Reset if no longer detected
                    securityState.violationCount = Math.max(0, securityState.violationCount - 1);
                    
                    if (securityState.violationCount === 0) {
                        securityState.devtoolsDetected = false;
                        SecurityResponse.restoreAccess();
                    }
                }
            } catch (error) {
                // Ignore errors to prevent false positives
                console.warn('Security detection error:', error);
            }
            
            return detected;
        }
    };
    
    // Security response system
    const SecurityResponse = {
        handleViolation: function(methods) {
            if (securityState.isBlocked) return;
            
            securityState.isBlocked = true;
            
            // Gentle page protection
            document.body.style.filter = 'blur(10px)';
            document.body.style.userSelect = 'none';
            
            this.showWarning(methods);
            this.logEvent('devtools_detected', methods);
        },
        
        showWarning: function(methods) {
            const overlay = document.createElement('div');
            overlay.id = 'security-warning';
            overlay.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0, 0, 0, 0.9); color: #ff4444; display: flex;
                align-items: center; justify-content: center; z-index: 999999;
                font-family: Arial, sans-serif; text-align: center;
            `;
            
            overlay.innerHTML = `
                <div style="max-width: 500px; padding: 40px;">
                    <h1 style="color: #ff4444; font-size: 32px; margin-bottom: 20px;">🛡️ Security Notice</h1>
                    <p style="font-size: 18px; margin-bottom: 20px;">Developer tools access detected.</p>
                    <p style="font-size: 14px; color: #ccc; margin-bottom: 30px;">
                        Please close developer tools to continue using the application.
                    </p>
                    <button onclick="window.location.reload()" style="
                        background: #ff4444; color: white; border: none;
                        padding: 12px 24px; border-radius: 5px; cursor: pointer;
                        font-size: 16px;
                    ">Reload Page</button>
                </div>
            `;
            
            // Remove existing warning
            const existing = document.getElementById('security-warning');
            if (existing) existing.remove();
            
            document.body.appendChild(overlay);
        },
        
        restoreAccess: function() {
            if (!securityState.isBlocked) return;
            
            document.body.style.filter = '';
            document.body.style.userSelect = '';
            
            const overlay = document.getElementById('security-warning');
            if (overlay) overlay.remove();
            
            securityState.isBlocked = false;
            this.logEvent('access_restored', []);
        },
        
        logEvent: function(eventType, methods) {
            // Simple logging
            console.warn(`Security Event: ${eventType}`, methods);
            
            // Send to server if available
            if (window.fetch) {
                const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
                
                fetch('/accounts/api/security-log/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': csrfToken
                    },
                    body: JSON.stringify({
                        event_type: eventType,
                        details: `Methods: ${methods.join(', ')}`,
                        timestamp: new Date().toISOString(),
                        url: window.location.href
                    })
                }).catch(() => {}); // Silent fail
            }
        }
    };
    
    // Keyboard shortcut blocking
    const KeyboardBlocker = {
        init: function() {
            document.addEventListener('keydown', this.handleKeyDown.bind(this), true);
        },
        
        handleKeyDown: function(event) {
            // Block common DevTools shortcuts
            const blockedKeys = [
                { key: 'F12' },
                { key: 'I', ctrl: true, shift: true },
                { key: 'J', ctrl: true, shift: true },
                { key: 'U', ctrl: true },
                { key: 'S', ctrl: true },
                { key: 'P', ctrl: true }
            ];
            
            for (const blocked of blockedKeys) {
                if (event.key === blocked.key &&
                    (!blocked.ctrl || event.ctrlKey) &&
                    (!blocked.shift || event.shiftKey)) {
                    
                    event.preventDefault();
                    event.stopPropagation();
                    
                    // Trigger security detection
                    DevToolsDetector.runDetection();
                    return false;
                }
            }
        }
    };
    
    // Context menu blocking
    const ContextMenuBlocker = {
        init: function() {
            document.addEventListener('contextmenu', this.handleContextMenu.bind(this), true);
        },
        
        handleContextMenu: function(event) {
            event.preventDefault();
            event.stopPropagation();
            
            // Show custom message
            const message = document.createElement('div');
            message.style.cssText = `
                position: fixed; top: ${event.clientY}px; left: ${event.clientX}px;
                background: #333; color: white; padding: 10px; border-radius: 5px;
                z-index: 999999; font-family: Arial; font-size: 14px;
            `;
            message.textContent = 'Right-click disabled for security';
            document.body.appendChild(message);
            
            setTimeout(() => message.remove(), 2000);
            
            return false;
        }
    };
    
    // Initialize security system
    const initSecurity = function() {
        // Initialize components
        KeyboardBlocker.init();
        ContextMenuBlocker.init();
        
        // Start detection with conservative intervals
        setInterval(() => {
            DevToolsDetector.runDetection();
        }, 5000); // Check every 5 seconds
        
        // Initial check after page loads
        setTimeout(() => {
            DevToolsDetector.runDetection();
        }, 3000);
        
        console.log('🛡️ Reliable Security Active');
    };
    
    // Start when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initSecurity);
    } else {
        initSecurity();
    }
    
    // Export interface
    window.ForgeXReliableSecurity = {
        version: '1.0.0',
        status: 'active',
        violations: () => securityState.violationCount,
        isBlocked: () => securityState.isBlocked
    };
    
})();
