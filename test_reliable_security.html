<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reliable Security Test - ForgeX</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .status.good {
            background: #e8f5e8;
            border: 2px solid #4CAF50;
            color: #2e7d32;
        }
        
        .status.warning {
            background: #fff8e1;
            border: 2px solid #ff9800;
            color: #f57c00;
        }
        
        .status.error {
            background: #ffebee;
            border: 2px solid #f44336;
            color: #c62828;
        }
        
        .test-button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        
        .test-button:hover {
            background: #1976D2;
        }
        
        .test-results {
            background: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 14px;
        }
        
        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #1976d2;
        }
    </style>
</head>
<body>
    <!-- Global Config -->
    <script>
        window.FORGEX_CONFIG = {
            debug_mode: false, // Set to false to test security
            timestamp: Date.now()
        };
    </script>

    <div class="container">
        <h1>🛡️ Reliable Security System Test</h1>
        <p>This page tests the improved security system that prevents false positives while maintaining protection.</p>
        
        <div class="status good" id="status-indicator">
            <strong>✅ System Status:</strong> <span id="status-text">Loading...</span>
        </div>
    </div>

    <div class="container">
        <h2>🧪 Security Features Test</h2>
        
        <div class="instructions">
            <h3>What Should Work (No False Positives):</h3>
            <ul>
                <li>✅ Normal page browsing and interaction</li>
                <li>✅ Text selection and copying</li>
                <li>✅ Form input and editing</li>
                <li>✅ Scrolling and resizing browser window</li>
                <li>✅ Opening other tabs or applications</li>
                <li>✅ Normal keyboard shortcuts (Ctrl+C, Ctrl+V, etc.)</li>
            </ul>
        </div>
        
        <div class="instructions">
            <h3>What Should Be Blocked:</h3>
            <ul>
                <li>❌ F12 key (DevTools)</li>
                <li>❌ Ctrl+Shift+I (DevTools)</li>
                <li>❌ Ctrl+Shift+J (Console)</li>
                <li>❌ Ctrl+U (View Source)</li>
                <li>❌ Right-click context menu</li>
                <li>❌ Browser menu → Developer Tools (should trigger warning after multiple attempts)</li>
            </ul>
        </div>
        
        <button class="test-button" onclick="testNormalFunctions()">Test Normal Functions</button>
        <button class="test-button" onclick="testSecurityStatus()">Check Security Status</button>
        <button class="test-button" onclick="testTextSelection()">Test Text Selection</button>
        
        <div class="test-results" id="test-results">
            <strong>Test Results:</strong><br>
            Click a test button to start...
        </div>
    </div>

    <div class="container">
        <h2>📝 Interactive Test Area</h2>
        <p>Use this area to test normal functionality:</p>
        
        <div style="margin: 20px 0;">
            <label for="test-input">Test Input Field:</label><br>
            <input type="text" id="test-input" placeholder="Type here to test input functionality" style="width: 100%; padding: 10px; margin: 10px 0;">
        </div>
        
        <div style="margin: 20px 0;">
            <label for="test-textarea">Test Text Area:</label><br>
            <textarea id="test-textarea" rows="4" placeholder="Type here and try selecting text..." style="width: 100%; padding: 10px; margin: 10px 0;">This is sample text that you should be able to select and copy. Try selecting this text with your mouse or keyboard shortcuts like Ctrl+A.</textarea>
        </div>
        
        <div style="margin: 20px 0; padding: 15px; background: #f0f0f0; border-radius: 5px;">
            <strong>Selectable Text:</strong> This paragraph contains text that should be selectable for normal reading and copying. You should be able to highlight this text without triggering any security warnings. The security system should only activate when actual developer tools are accessed.
        </div>
    </div>

    <div class="container">
        <h2>🔍 Security Event Log</h2>
        <div id="security-log" style="background: #f9f9f9; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto;">
            <strong>Security Events:</strong><br>
            <span id="log-content">No events logged yet...</span>
        </div>
        <button class="test-button" onclick="clearLog()">Clear Log</button>
    </div>

    <!-- Load Reliable Security -->
    <script src="/static/js/security/reliable-security.js"></script>

    <!-- Test Functions -->
    <script>
        let logEntries = [];
        
        function updateStatus() {
            const statusText = document.getElementById('status-text');
            const statusIndicator = document.getElementById('status-indicator');
            
            if (window.ForgeXReliableSecurity) {
                statusText.textContent = `Active (v${window.ForgeXReliableSecurity.version}) - Violations: ${window.ForgeXReliableSecurity.violations()}`;
                statusIndicator.className = 'status good';
            } else {
                statusText.textContent = 'Security system not loaded';
                statusIndicator.className = 'status warning';
            }
        }
        
        function testNormalFunctions() {
            const results = document.getElementById('test-results');
            let tests = [];
            
            // Test 1: Text selection
            try {
                const testText = document.getElementById('test-textarea');
                testText.select();
                tests.push('✅ Text selection works');
            } catch (e) {
                tests.push('❌ Text selection failed: ' + e.message);
            }
            
            // Test 2: Input functionality
            try {
                const testInput = document.getElementById('test-input');
                testInput.value = 'Test input works!';
                testInput.focus();
                tests.push('✅ Input functionality works');
            } catch (e) {
                tests.push('❌ Input functionality failed: ' + e.message);
            }
            
            // Test 3: Console access (should be limited but not break page)
            try {
                console.log('Test console message');
                tests.push('✅ Console access controlled');
            } catch (e) {
                tests.push('⚠️ Console access blocked: ' + e.message);
            }
            
            // Test 4: Window resize simulation
            try {
                window.dispatchEvent(new Event('resize'));
                tests.push('✅ Window resize events work');
            } catch (e) {
                tests.push('❌ Window resize failed: ' + e.message);
            }
            
            results.innerHTML = '<strong>Normal Function Tests:</strong><br>' + tests.join('<br>');
        }
        
        function testSecurityStatus() {
            const results = document.getElementById('test-results');
            let status = [];
            
            if (window.ForgeXReliableSecurity) {
                status.push(`✅ Security system loaded: v${window.ForgeXReliableSecurity.version}`);
                status.push(`📊 Current status: ${window.ForgeXReliableSecurity.status}`);
                status.push(`🚨 Violations detected: ${window.ForgeXReliableSecurity.violations()}`);
                status.push(`🔒 Currently blocked: ${window.ForgeXReliableSecurity.isBlocked()}`);
            } else {
                status.push('❌ Security system not detected');
            }
            
            // Check for security events
            const securityWarning = document.getElementById('security-warning');
            if (securityWarning) {
                status.push('⚠️ Security warning currently active');
            } else {
                status.push('✅ No security warnings active');
            }
            
            results.innerHTML = '<strong>Security Status:</strong><br>' + status.join('<br>');
        }
        
        function testTextSelection() {
            const results = document.getElementById('test-results');
            let tests = [];
            
            try {
                // Test selecting different elements
                const elements = ['test-input', 'test-textarea'];
                
                elements.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.select();
                        tests.push(`✅ Selection works for ${id}`);
                    }
                });
                
                // Test programmatic selection
                const selection = window.getSelection();
                selection.selectAllChildren(document.body);
                tests.push('✅ Programmatic selection works');
                selection.removeAllRanges();
                tests.push('✅ Selection clearing works');
                
            } catch (e) {
                tests.push('❌ Text selection test failed: ' + e.message);
            }
            
            results.innerHTML = '<strong>Text Selection Tests:</strong><br>' + tests.join('<br>');
        }
        
        function logSecurityEvent(message) {
            logEntries.push(`[${new Date().toLocaleTimeString()}] ${message}`);
            updateSecurityLog();
        }
        
        function updateSecurityLog() {
            const logContent = document.getElementById('log-content');
            if (logEntries.length === 0) {
                logContent.textContent = 'No events logged yet...';
            } else {
                logContent.innerHTML = logEntries.join('<br>');
            }
        }
        
        function clearLog() {
            logEntries = [];
            updateSecurityLog();
        }
        
        // Monitor for security events
        const originalConsoleWarn = console.warn;
        console.warn = function(...args) {
            const message = args.join(' ');
            if (message.includes('Security Event')) {
                logSecurityEvent(message);
            }
            originalConsoleWarn.apply(console, args);
        };
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus();
            logSecurityEvent('Page loaded successfully');
            
            // Update status periodically
            setInterval(updateStatus, 2000);
            
            // Log normal interactions
            document.addEventListener('click', () => logSecurityEvent('Normal click interaction'));
            document.addEventListener('keydown', (e) => {
                if (!e.ctrlKey && !e.shiftKey && e.key.length === 1) {
                    logSecurityEvent(`Normal keypress: ${e.key}`);
                }
            });
        });
        
        // Test that normal operations don't trigger security
        setTimeout(() => {
            logSecurityEvent('5-second test: No false positives detected');
        }, 5000);
        
        setTimeout(() => {
            logSecurityEvent('10-second test: System stable');
        }, 10000);
    </script>
</body>
</html>
