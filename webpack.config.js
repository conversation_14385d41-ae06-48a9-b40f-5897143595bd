const path = require('path');
const TerserPlugin = require('terser-webpack-plugin');
const JavaScriptObfuscator = require('webpack-obfuscator');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CryptoJS = require('crypto-js');

module.exports = (env, argv) => {
  const isProduction = argv.mode === 'production';
  const isSecure = env && env.secure;
  const isAdmin = env && env.admin;
  
  // Generate dynamic API keys for obfuscation
  const apiKey = CryptoJS.lib.WordArray.random(32).toString();
  const encryptionKey = CryptoJS.lib.WordArray.random(32).toString();
  
  const config = {
    entry: {
      // Main application bundles
      'main': './static/js/main.js',
      'editor': './static/js/editor-bundle.js',
      'learn': './static/js/learn-bundle.js',
      'collaborate': './static/js/collaborate-bundle.js',
      'mentorship': './static/js/mentorship-bundle.js',
      'accounts': './static/js/accounts-bundle.js',
      
      // Security and utilities
      'security': './static/js/security/security-bundle.js',
      'api-client': './static/js/api/api-client.js'
    },
    
    output: {
      path: path.resolve(__dirname, 'static/dist'),
      filename: isSecure ? '[name].[contenthash].min.js' : '[name].bundle.js',
      chunkFilename: isSecure ? '[name].[contenthash].chunk.js' : '[name].chunk.js',
      clean: true,
      publicPath: '/static/dist/'
    },
    
    module: {
      rules: [
        {
          test: /\.js$/,
          exclude: /node_modules/,
          use: {
            loader: 'babel-loader',
            options: {
              presets: ['@babel/preset-env'],
              plugins: []
            }
          }
        },
        {
          test: /\.css$/,
          use: [
            isProduction ? MiniCssExtractPlugin.loader : 'style-loader',
            'css-loader'
          ]
        }
      ]
    },
    
    plugins: [
      new MiniCssExtractPlugin({
        filename: isSecure ? '[name].[contenthash].css' : '[name].css'
      })
    ],
    
    optimization: {
      minimize: isProduction,
      minimizer: [
        new TerserPlugin({
          terserOptions: {
            compress: {
              drop_console: isSecure && !isAdmin,
              drop_debugger: isSecure && !isAdmin,
              pure_funcs: isSecure ? ['console.log', 'console.info', 'console.debug'] : []
            },
            mangle: isSecure ? {
              properties: {
                regex: /^_/
              }
            } : false,
            format: {
              comments: isAdmin
            }
          }
        })
      ],
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all'
          },
          common: {
            name: 'common',
            minChunks: 2,
            chunks: 'all',
            enforce: true
          }
        }
      }
    },
    
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'static/js'),
        '@security': path.resolve(__dirname, 'static/js/security'),
        '@api': path.resolve(__dirname, 'static/js/api'),
        '@utils': path.resolve(__dirname, 'static/js/utils')
      }
    },
    
    devtool: isAdmin ? 'source-map' : (isSecure ? false : 'eval-source-map'),
    
    devServer: {
      contentBase: path.join(__dirname, 'static/dist'),
      compress: true,
      port: 9000,
      hot: true
    }
  };
  
  // Add ENHANCED obfuscation for secure builds (non-admin)
  if (isSecure && !isAdmin) {
    config.plugins.push(
      new JavaScriptObfuscator({
        // Core obfuscation settings
        compact: true,
        controlFlowFlattening: true,
        controlFlowFlatteningThreshold: 0.75,
        deadCodeInjection: true,
        deadCodeInjectionThreshold: 0.4,
        debugProtection: true,
        debugProtectionInterval: 2000,
        disableConsoleOutput: true,

        // Identifier obfuscation
        identifierNamesGenerator: 'hexadecimal',
        identifiersPrefix: 'forgex_',
        renameGlobals: true,
        renameProperties: false,

        // String obfuscation (enhanced)
        stringArray: true,
        stringArrayCallsTransform: true,
        stringArrayCallsTransformThreshold: 0.8,
        stringArrayEncoding: ['base64', 'rc4'],
        stringArrayIndexShift: true,
        stringArrayRotate: true,
        stringArrayShuffle: true,
        stringArrayWrappersCount: 2,
        stringArrayWrappersChainedCalls: true,
        stringArrayWrappersParametersMaxCount: 4,
        stringArrayWrappersType: 'function',
        stringArrayThreshold: 0.8,

        // Advanced obfuscation
        selfDefending: true,
        simplify: true,
        splitStrings: true,
        splitStringsChunkLength: 5,
        numbersToExpressions: true,
        transformObjectKeys: true,
        unicodeEscapeSequence: true,

        // Performance vs Security balance
        target: 'browser',
        stringArrayThreshold: 0.75,
        unicodeEscapeSequence: false,
        target: 'browser',
        transformObjectKeys: false,
        reservedNames: [
          '^_',
          'require',
          'exports',
          'module',
          'window',
          'document',
          'console'
        ]
      }, ['vendors.*.js'])
    );
  }
  
  return config;
};
