/**
 * ForgeX Security Bundle - Enhanced Protection System
 * This file contains all security measures and will be heavily obfuscated
 */

(function() {
    'use strict';
    
    // Security configuration
    const SECURITY_CONFIG = {
        version: '3.0.0',
        buildTime: Date.now(),
        checkInterval: 200,
        detectionThreshold: 2,
        maxViolations: 5
    };
    
    // Check if user is admin
    const isAdmin = window.FORGEX_CONFIG && window.FORGEX_CONFIG.debug_mode;
    
    if (isAdmin) {
        console.log('🔓 Security bundle disabled for admin user');
        return;
    }
    
    // Security state management
    let securityState = {
        devtoolsOpen: false,
        violationCount: 0,
        detectionCount: 0,
        lastCheck: Date.now(),
        focusBlurPattern: [],
        isBlocked: false
    };
    
    // Advanced DevTools Detection System
    const DevToolsDetector = {
        // Method 1: Console object manipulation detection
        detectConsoleAccess: function() {
            const element = new Image();
            let detected = false;
            
            Object.defineProperty(element, 'id', {
                get: function() {
                    detected = true;
                    return 'security_violation_detected';
                }
            });
            
            // Multiple console access patterns
            console.log('%c', element);
            console.dir(element);
            console.table([element]);
            console.group(element);
            console.groupEnd();
            console.clear();
            
            return detected;
        },
        
        // Method 2: Window dimension analysis
        detectWindowDimensions: function() {
            const heightDiff = window.outerHeight - window.innerHeight;
            const widthDiff = window.outerWidth - window.innerWidth;
            
            // Sensitive thresholds for better detection
            return (heightDiff > 60 || widthDiff > 60);
        },
        
        // Method 3: Performance timing analysis
        detectPerformanceTiming: function() {
            const start = performance.now();
            debugger; // Pauses execution if DevTools are open
            const end = performance.now();
            
            return (end - start) > 30; // Very sensitive timing
        },
        
        // Method 4: Screen resolution analysis
        detectScreenResolution: function() {
            const screenRatio = window.screen.availHeight / window.innerHeight;
            const expectedRatio = window.screen.availWidth / window.innerWidth;
            
            return Math.abs(screenRatio - expectedRatio) > 0.25;
        },
        
        // Method 5: Focus/blur pattern analysis
        detectFocusBlurPattern: function() {
            if (securityState.focusBlurPattern.length > 5) {
                securityState.focusBlurPattern = securityState.focusBlurPattern.slice(-5);
            }
            
            const rapidChanges = securityState.focusBlurPattern.filter((event, index) => {
                if (index === 0) return false;
                return event.timestamp - securityState.focusBlurPattern[index - 1].timestamp < 50;
            });
            
            return rapidChanges.length > 2;
        },
        
        // Method 6: Browser-specific object detection
        detectBrowserObjects: function() {
            return !!(
                (window.chrome && window.chrome.runtime) ||
                (window.opr && window.opr.addons) ||
                (window.safari && window.safari.pushNotification) ||
                (window.InstallTrigger !== undefined) ||
                (window.DeviceOrientationEvent && !/Mobile|Android|iPhone|iPad/i.test(navigator.userAgent))
            );
        },
        
        // Method 7: DOM mutation monitoring
        detectDOMMutations: function() {
            let mutationDetected = false;
            
            const observer = new MutationObserver(() => {
                mutationDetected = true;
            });
            
            observer.observe(document.documentElement, {
                attributes: true,
                childList: true,
                subtree: true
            });
            
            setTimeout(() => observer.disconnect(), 50);
            return mutationDetected;
        },
        
        // Method 8: Network request monitoring
        detectNetworkActivity: function() {
            const originalFetch = window.fetch;
            let networkSuspicious = false;
            
            window.fetch = function(...args) {
                if (securityState.detectionCount > 2) {
                    networkSuspicious = true;
                }
                return originalFetch.apply(this, args);
            };
            
            return networkSuspicious;
        },
        
        // Comprehensive detection runner
        runDetection: function() {
            const now = Date.now();
            
            // Throttle checks
            if (now - securityState.lastCheck < SECURITY_CONFIG.checkInterval) return false;
            securityState.lastCheck = now;
            
            let detected = false;
            let methods = [];
            
            try {
                // Run all detection methods
                const detectionMethods = [
                    { name: 'console', method: this.detectConsoleAccess },
                    { name: 'window', method: this.detectWindowDimensions },
                    { name: 'timing', method: this.detectPerformanceTiming },
                    { name: 'screen', method: this.detectScreenResolution },
                    { name: 'focus', method: this.detectFocusBlurPattern },
                    { name: 'browser', method: this.detectBrowserObjects },
                    { name: 'dom', method: this.detectDOMMutations },
                    { name: 'network', method: this.detectNetworkActivity }
                ];
                
                detectionMethods.forEach(detector => {
                    try {
                        if (detector.method.call(this)) {
                            detected = true;
                            methods.push(detector.name);
                        }
                    } catch (e) {
                        // If detection method fails, assume threat
                        detected = true;
                        methods.push(detector.name + '_error');
                    }
                });
                
                if (detected) {
                    securityState.detectionCount++;
                    
                    if (securityState.detectionCount >= SECURITY_CONFIG.detectionThreshold && !securityState.devtoolsOpen) {
                        securityState.devtoolsOpen = true;
                        SecurityResponse.handleViolation(methods);
                    }
                } else {
                    securityState.detectionCount = Math.max(0, securityState.detectionCount - 1);
                    
                    if (securityState.detectionCount === 0 && securityState.devtoolsOpen) {
                        securityState.devtoolsOpen = false;
                        SecurityResponse.restoreAccess();
                    }
                }
            } catch (error) {
                // Global error in detection system
                securityState.detectionCount++;
                if (securityState.detectionCount >= 3 && !securityState.devtoolsOpen) {
                    securityState.devtoolsOpen = true;
                    SecurityResponse.handleViolation(['system_error']);
                }
            }
            
            return detected;
        }
    };
    
    // Security Response System
    const SecurityResponse = {
        handleViolation: function(methods) {
            securityState.violationCount++;
            securityState.isBlocked = true;
            
            // Immediate page protection
            document.body.style.filter = 'blur(20px)';
            document.body.style.userSelect = 'none';
            document.body.style.pointerEvents = 'none';
            
            this.showSecurityOverlay(methods);
            this.logSecurityEvent('devtools_violation', methods);
            
            // Escalate if too many violations
            if (securityState.violationCount >= SECURITY_CONFIG.maxViolations) {
                this.escalateViolation();
            }
        },
        
        showSecurityOverlay: function(methods) {
            const overlay = document.createElement('div');
            overlay.id = 'security-violation-overlay';
            overlay.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0, 0, 0, 0.98); color: #ff4444; display: flex;
                align-items: center; justify-content: center; z-index: 999999;
                font-family: Arial, sans-serif; text-align: center;
                backdrop-filter: blur(15px);
            `;
            
            overlay.innerHTML = `
                <div style="max-width: 700px; padding: 50px;">
                    <h1 style="color: #ff4444; font-size: 42px; margin-bottom: 20px;">🛡️ SECURITY BREACH</h1>
                    <p style="font-size: 22px; margin-bottom: 30px; font-weight: bold;">Unauthorized Developer Tools Access Detected</p>
                    <div style="background: rgba(255, 68, 68, 0.15); padding: 25px; border-radius: 10px; margin: 30px 0;">
                        <p style="font-size: 16px; color: #ff6666; margin: 0;">
                            ⚠️ This security violation has been logged and reported to system administrators.
                        </p>
                    </div>
                    <p style="font-size: 14px; color: #888; margin-bottom: 40px;">
                        Detection methods: ${methods.join(', ')}<br>
                        Violation #${securityState.violationCount} | Session ID: ${Date.now()}
                    </p>
                    <button onclick="window.location.reload()" style="
                        background: linear-gradient(45deg, #ff4444, #cc3333);
                        color: white; border: none; padding: 18px 35px;
                        border-radius: 10px; cursor: pointer; font-size: 18px;
                        font-weight: bold; box-shadow: 0 6px 20px rgba(255, 68, 68, 0.4);
                    ">🔄 Reload and Comply</button>
                </div>
            `;
            
            // Remove existing overlay
            const existing = document.getElementById('security-violation-overlay');
            if (existing) existing.remove();
            
            document.body.appendChild(overlay);
            overlay.style.pointerEvents = 'auto';
        },
        
        restoreAccess: function() {
            if (!securityState.isBlocked) return;
            
            document.body.style.filter = '';
            document.body.style.userSelect = '';
            document.body.style.pointerEvents = '';
            
            const overlay = document.getElementById('security-violation-overlay');
            if (overlay) overlay.remove();
            
            securityState.isBlocked = false;
            this.logSecurityEvent('access_restored', []);
        },
        
        escalateViolation: function() {
            // Severe security response for repeated violations
            document.body.innerHTML = `
                <div style="display: flex; align-items: center; justify-content: center; height: 100vh; background: #000; color: #ff4444; font-family: Arial;">
                    <div style="text-align: center;">
                        <h1 style="font-size: 48px; margin-bottom: 20px;">🚨 SECURITY LOCKDOWN</h1>
                        <p style="font-size: 20px;">Multiple security violations detected. Access terminated.</p>
                        <p style="font-size: 14px; color: #888; margin-top: 30px;">Contact administrator for assistance.</p>
                    </div>
                </div>
            `;
            
            this.logSecurityEvent('security_lockdown', ['escalated_violations']);
        },
        
        logSecurityEvent: function(eventType, methods) {
            if (!window.fetch) return;
            
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value ||
                             document.querySelector('meta[name=csrf-token]')?.content ||
                             this.getCookie('csrftoken') || '';
            
            fetch('/accounts/api/security-log/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify({
                    event_type: eventType,
                    details: `Methods: ${methods.join(', ')} | Violations: ${securityState.violationCount}`,
                    user_agent: navigator.userAgent,
                    timestamp: new Date().toISOString(),
                    url: window.location.href,
                    security_version: SECURITY_CONFIG.version
                })
            }).catch(() => {}); // Silent fail
        },
        
        getCookie: function(name) {
            const cookies = document.cookie.split(';');
            for (let cookie of cookies) {
                const [key, value] = cookie.trim().split('=');
                if (key === name) return decodeURIComponent(value);
            }
            return null;
        }
    };
    
    // Event monitoring system
    const EventMonitor = {
        init: function() {
            // Window events
            window.addEventListener('resize', () => DevToolsDetector.runDetection());
            window.addEventListener('focus', () => {
                securityState.focusBlurPattern.push({ type: 'focus', timestamp: Date.now() });
                DevToolsDetector.runDetection();
            });
            window.addEventListener('blur', () => {
                securityState.focusBlurPattern.push({ type: 'blur', timestamp: Date.now() });
                DevToolsDetector.runDetection();
            });
            
            // Document events
            document.addEventListener('visibilitychange', () => DevToolsDetector.runDetection());
            document.addEventListener('keydown', () => DevToolsDetector.runDetection());
            document.addEventListener('contextmenu', () => DevToolsDetector.runDetection());
            
            // Page lifecycle events
            window.addEventListener('beforeunload', () => DevToolsDetector.runDetection());
            window.addEventListener('pagehide', () => DevToolsDetector.runDetection());
            window.addEventListener('pageshow', () => DevToolsDetector.runDetection());
        }
    };
    
    // Initialize security system
    const initSecurity = function() {
        console.log('🛡️ Enhanced Security System Loading...');
        
        // Start event monitoring
        EventMonitor.init();
        
        // Start continuous detection with varying intervals
        const intervals = [150, 250, 350, 500, 750];
        let intervalIndex = 0;
        
        const scheduleDetection = () => {
            const interval = intervals[intervalIndex % intervals.length];
            intervalIndex++;
            
            setTimeout(() => {
                DevToolsDetector.runDetection();
                scheduleDetection();
            }, interval);
        };
        
        scheduleDetection();
        
        // Immediate detection
        setTimeout(() => DevToolsDetector.runDetection(), 50);
        
        console.log('🛡️ Enhanced Security Active');
    };
    
    // Start security system
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initSecurity);
    } else {
        initSecurity();
    }
    
    // Export security interface (will be obfuscated)
    window.ForgeXSecurityEnhanced = {
        version: SECURITY_CONFIG.version,
        status: 'active',
        violations: () => securityState.violationCount,
        isBlocked: () => securityState.isBlocked
    };
    
})();
